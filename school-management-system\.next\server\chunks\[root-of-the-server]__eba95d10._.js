module.exports = [
"[project]/school-management-system/.next-internal/server/app/api/admin/settings/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/querystring [external] (querystring, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}),
"[externals]/buffer [external] (buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}),
"[project]/school-management-system/src/lib/db.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "prisma",
    ()=>prisma
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}),
"[project]/school-management-system/src/lib/auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "authOptions",
    ()=>authOptions
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next-auth@4.24.11_next@15.5_c2997e08dc728cda550a5ed739df8c9e/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$bcryptjs$40$3$2e$0$2e$2$2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/src/lib/db.ts [app-route] (ecmascript)");
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                try {
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                        where: {
                            email: credentials.email
                        }
                    });
                    if (!user || !user.hashedPassword) {
                        return null;
                    }
                    const isCorrectPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$bcryptjs$40$3$2e$0$2e$2$2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.hashedPassword);
                    if (!isCorrectPassword) {
                        return null;
                    }
                    return {
                        id: user.id,
                        email: user.email,
                        name: `${user.firstName} ${user.lastName}`,
                        role: user.role,
                        firstName: user.firstName,
                        lastName: user.lastName
                    };
                } catch (error) {
                    console.error('Auth error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt',
        maxAge: 24 * 60 * 60
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.firstName = user.firstName;
                token.lastName = user.lastName;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.firstName = token.firstName;
                session.user.lastName = token.lastName;
            }
            return session;
        }
    },
    pages: {
        signIn: '/login',
        error: '/login'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}),
"[project]/school-management-system/src/app/api/admin/settings/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST,
    "buildSettingsUpdates",
    ()=>buildSettingsUpdates
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/node_modules/.pnpm/next-auth@4.24.11_next@15.5_c2997e08dc728cda550a5ed739df8c9e/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/school-management-system/src/lib/db.ts [app-route] (ecmascript)");
;
;
;
;
function buildSettingsUpdates(type, data) {
    const settingsToUpdate = [];
    switch(type){
        case 'general':
            settingsToUpdate.push({
                key: 'school_name',
                value: data.name
            }, {
                key: 'school_address',
                value: data.address
            }, {
                key: 'school_phone',
                value: data.phone
            }, {
                key: 'school_email',
                value: data.email
            }, {
                key: 'school_website',
                value: data.website
            }, {
                key: 'school_principal',
                value: data.principal
            }, {
                key: 'school_established_year',
                value: data.establishedYear
            });
            break;
        case 'academic':
            settingsToUpdate.push({
                key: 'academic_year',
                value: data.academicYear
            }, {
                key: 'current_term',
                value: data.currentTerm
            }, {
                key: 'grading_system',
                value: data.gradingSystem
            }, {
                key: 'pass_percentage',
                value: data.passPercentage?.toString()
            }, {
                key: 'max_attendance_percentage',
                value: data.maxAttendancePercentage?.toString()
            });
            break;
        case 'notifications':
            settingsToUpdate.push({
                key: 'attendance_alerts',
                value: data.attendanceAlerts?.toString()
            }, {
                key: 'exam_results',
                value: data.examResults?.toString()
            }, {
                key: 'report_card_generation',
                value: data.reportCardGeneration?.toString()
            }, {
                key: 'system_updates',
                value: data.systemUpdates?.toString()
            });
            break;
        case 'security':
            settingsToUpdate.push({
                key: 'session_timeout',
                value: data.sessionTimeout?.toString()
            }, {
                key: 'password_policy',
                value: data.passwordPolicy
            }, {
                key: 'two_factor_auth',
                value: data.twoFactorAuth?.toString()
            }, {
                key: 'login_attempts',
                value: data.loginAttempts?.toString()
            });
            break;
        default:
            throw new Error('Invalid settings type');
    }
    // Ensure no undefined slips through which would cause Prisma validation error
    for (const s of settingsToUpdate){
        if (s.value === undefined) {
            throw new Error(`Missing value for setting key: ${s.key}`);
        }
    }
    return settingsToUpdate;
}
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || session.user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const type = searchParams.get('type');
        // Get all settings
        const settings = await __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].setting.findMany();
        // Convert settings array to object for easier access
        const settingsObject = settings.reduce((acc, setting)=>{
            acc[setting.key] = setting.value;
            return acc;
        }, {});
        // Return different data based on type parameter
        switch(type){
            case 'general':
                return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    schoolName: settingsObject['school_name'] || 'Advance School',
                    address: settingsObject['school_address'] || '123 Education Street, City, State 12345',
                    phone: settingsObject['school_phone'] || '+****************',
                    email: settingsObject['school_email'] || '<EMAIL>',
                    website: settingsObject['school_website'] || 'www.advanceschool.edu',
                    principal: settingsObject['school_principal'] || 'Dr. John Smith',
                    establishedYear: settingsObject['school_established_year'] || '1995'
                });
            case 'academic':
                return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    academicYear: settingsObject['academic_year'] || '2024-2025',
                    currentTerm: settingsObject['current_term'] || 'Term 1',
                    gradingSystem: settingsObject['grading_system'] || 'LETTER',
                    passPercentage: parseInt(settingsObject['pass_percentage']) || 40,
                    maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75
                });
            case 'notifications':
                return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    attendanceAlerts: settingsObject['attendance_alerts'] === 'true',
                    examResults: settingsObject['exam_results'] === 'true',
                    reportCardGeneration: settingsObject['report_card_generation'] === 'true',
                    systemUpdates: settingsObject['system_updates'] === 'true'
                });
            case 'security':
                return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,
                    passwordPolicy: settingsObject['password_policy'] || 'strong',
                    twoFactorAuth: settingsObject['two_factor_auth'] === 'true',
                    loginAttempts: settingsObject['login_attempts'] === 'true'
                });
            default:
                // Return all settings
                return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    general: {
                        schoolName: settingsObject['school_name'] || 'Advance School',
                        address: settingsObject['school_address'] || '123 Education Street, City, State 12345',
                        phone: settingsObject['school_phone'] || '+****************',
                        email: settingsObject['school_email'] || '<EMAIL>',
                        website: settingsObject['school_website'] || 'www.advanceschool.edu',
                        principal: settingsObject['school_principal'] || 'Dr. John Smith',
                        establishedYear: settingsObject['school_established_year'] || '1995'
                    },
                    academic: {
                        academicYear: settingsObject['academic_year'] || '2024-2025',
                        currentTerm: settingsObject['current_term'] || 'Term 1',
                        gradingSystem: settingsObject['grading_system'] || 'LETTER',
                        passPercentage: parseInt(settingsObject['pass_percentage']) || 40,
                        maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75
                    },
                    notifications: {
                        attendanceAlerts: settingsObject['attendance_alerts'] === 'true',
                        examResults: settingsObject['exam_results'] === 'true',
                        reportCardGeneration: settingsObject['report_card_generation'] === 'true',
                        systemUpdates: settingsObject['system_updates'] === 'true'
                    },
                    security: {
                        sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,
                        passwordPolicy: settingsObject['password_policy'] || 'strong',
                        twoFactorAuth: settingsObject['two_factor_auth'] === 'true',
                        loginAttempts: settingsObject['login_attempts'] === 'true'
                    }
                });
        }
    } catch (error) {
        console.error('Error fetching settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$2d$auth$40$4$2e$24$2e$11_next$40$15$2e$5_c2997e08dc728cda550a5ed739df8c9e$2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || session.user.role !== 'ADMIN') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { type, data } = body;
        let settingsToUpdate;
        try {
            settingsToUpdate = buildSettingsUpdates(type, data);
        } catch (e) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: e instanceof Error ? e.message : 'Invalid payload'
            }, {
                status: 400
            });
        }
        // Update or create settings
        for (const setting of settingsToUpdate){
            await __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].setting.upsert({
                where: {
                    key: setting.key
                },
                update: {
                    value: setting.value
                },
                create: {
                    key: setting.key,
                    value: setting.value
                }
            });
        }
        // Log the settings update
        await __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].auditLog.create({
            data: {
                action: 'SETTINGS_UPDATE',
                entity: 'SETTING',
                entityId: type,
                userId: session.user.id,
                meta: {
                    details: `Updated ${type} settings`,
                    ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
                }
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: `${type} settings updated successfully`,
            type,
            data
        });
    } catch (error) {
        console.error('Error updating settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$school$2d$management$2d$system$2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$5$2e$2_$40$babel$2b$core$40$7$2e$2_1d9756f0f263ecf6e82055b1d95a339c$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__eba95d10._.js.map