{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline: \"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,8UAAC;QAAI,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,sLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,4TAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,8UAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,4TAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/teachers/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\n\r\ninterface Teacher {\r\n  id: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  phone?: string;\r\n  dateOfBirth?: string;\r\n  gender?: string;\r\n  address?: string;\r\n  qualification?: string;\r\n  experience?: number;\r\n  joiningDate?: string;\r\n  salary?: number;\r\n  isActive: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  user: {\r\n    id: number;\r\n    email: string;\r\n    role: string;\r\n  };\r\n  classes: Array<{\r\n    id: number;\r\n    name: string;\r\n    section: {\r\n      id: number;\r\n      name: string;\r\n    };\r\n  }>;\r\n  subjects: Array<{\r\n    id: number;\r\n    name: string;\r\n  }>;\r\n}\r\n\r\nexport default function TeacherDetailPage({ params }: { params: Promise<{ id: string }> }) {\r\n  const router = useRouter();\r\n  const [teacher, setTeacher] = useState<Teacher | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [teacherId, setTeacherId] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const resolveParams = async () => {\r\n      try {\r\n        const resolvedParams = await params;\r\n        setTeacherId(resolvedParams.id);\r\n      } catch (err) {\r\n        setError('Failed to resolve route parameters');\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    resolveParams();\r\n  }, [params]);\r\n\r\n  useEffect(() => {\r\n    if (!teacherId) return;\r\n\r\n    const fetchTeacher = async () => {\r\n      try {\r\n        const response = await fetch(`/api/admin/teachers/${teacherId}`);\r\n        if (!response.ok) {\r\n          throw new Error('Failed to fetch teacher');\r\n        }\r\n        const data = await response.json();\r\n        setTeacher(data.teacher);\r\n      } catch (err: any) {\r\n        setError(err.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchTeacher();\r\n  }, [teacherId]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString();\r\n  };\r\n\r\n  const getGenderLabel = (gender: string) => {\r\n    switch (gender) {\r\n      case 'MALE': return 'Male';\r\n      case 'FEMALE': return 'Female';\r\n      case 'OTHER': return 'Other';\r\n      default: return 'Not specified';\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertDescription>{error}</AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  if (!teacher) {\r\n    return (\r\n      <Alert>\r\n        <AlertDescription>Teacher not found</AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold\">\r\n            {teacher.firstName} {teacher.lastName}\r\n          </h1>\r\n          <p className=\"text-gray-600\">{teacher.email}</p>\r\n        </div>\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}\r\n          >\r\n            Edit Teacher\r\n          </Button>\r\n          <Button onClick={() => router.push('/admin/teachers')}>\r\n            Back to List\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n        {/* Personal Information */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Personal Information</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Full Name</label>\r\n              <p className=\"text-lg\">{teacher.firstName} {teacher.lastName}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Email</label>\r\n              <p className=\"text-lg\">{teacher.email}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Phone</label>\r\n              <p className=\"text-lg\">{teacher.phone || 'Not provided'}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Gender</label>\r\n              <p className=\"text-lg\">{teacher.gender ? getGenderLabel(teacher.gender) : 'Not specified'}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Date of Birth</label>\r\n              <p className=\"text-lg\">\r\n                {teacher.dateOfBirth ? formatDate(teacher.dateOfBirth) : 'Not provided'}\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Address</label>\r\n              <p className=\"text-lg\">{teacher.address || 'Not provided'}</p>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Professional Information */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Professional Information</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Qualification</label>\r\n              <p className=\"text-lg\">{teacher.qualification || 'Not provided'}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Experience</label>\r\n              <p className=\"text-lg\">\r\n                {teacher.experience ? `${teacher.experience} years` : 'Not specified'}\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Joining Date</label>\r\n              <p className=\"text-lg\">\r\n                {teacher.joiningDate ? formatDate(teacher.joiningDate) : 'Not provided'}\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Salary</label>\r\n              <p className=\"text-lg\">\r\n                {teacher.salary ? `$${teacher.salary.toLocaleString()}` : 'Not specified'}\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Status</label>\r\n              <div className=\"mt-1\">\r\n                <Badge variant={teacher.isActive ? 'default' : 'secondary'}>\r\n                  {teacher.isActive ? 'Active' : 'Inactive'}\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Account Information */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Account Information</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">User ID</label>\r\n              <p className=\"text-lg\">{teacher.user.id}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Role</label>\r\n              <p className=\"text-lg\">{teacher.user.role}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Created</label>\r\n              <p className=\"text-lg\">{formatDate(teacher.createdAt)}</p>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-500\">Last Updated</label>\r\n              <p className=\"text-lg\">{formatDate(teacher.updatedAt)}</p>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Classes and Subjects */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Assigned Classes</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {teacher.classes && teacher.classes.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {teacher.classes.map((cls) => (\r\n                  <Badge key={cls.id} variant=\"secondary\" className=\"mr-2 mb-2\">\r\n                    {cls.name} {cls.section.name}\r\n                  </Badge>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <p className=\"text-gray-500\">No classes assigned</p>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Teaching Subjects</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            {teacher.subjects && teacher.subjects.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {teacher.subjects.map((subject) => (\r\n                  <Badge key={subject.id} variant=\"outline\" className=\"mr-2 mb-2\">\r\n                    {subject.name}\r\n                  </Badge>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <p className=\"text-gray-500\">No subjects assigned</p>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA4Ce,SAAS,kBAAkB,KAA+C;QAA/C,EAAE,MAAM,EAAuC,GAA/C;;IACxC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAiB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,0TAAQ,EAAgB;IAE1D,IAAA,2TAAS;uCAAC;YACR,MAAM;6DAAgB;oBACpB,IAAI;wBACF,MAAM,iBAAiB,MAAM;wBAC7B,aAAa,eAAe,EAAE;oBAChC,EAAE,OAAO,KAAK;wBACZ,SAAS;wBACT,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG;QAAC;KAAO;IAEX,IAAA,2TAAS;uCAAC;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM;4DAAe;oBACnB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAgC,OAAV;wBACpD,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,WAAW,KAAK,OAAO;oBACzB,EAAE,OAAO,KAAU;wBACjB,SAAS,IAAI,OAAO;oBACtB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8UAAC;YAAI,WAAU;sBACb,cAAA,8UAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,8UAAC,+KAAK;YAAC,SAAQ;sBACb,cAAA,8UAAC,0LAAgB;0BAAE;;;;;;;;;;;IAGzB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8UAAC,+KAAK;sBACJ,cAAA,8UAAC,0LAAgB;0BAAC;;;;;;;;;;;IAGxB;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BACb,8UAAC;gBAAI,WAAU;;kCACb,8UAAC;;0CACC,8UAAC;gCAAG,WAAU;;oCACX,QAAQ,SAAS;oCAAC;oCAAE,QAAQ,QAAQ;;;;;;;0CAEvC,8UAAC;gCAAE,WAAU;0CAAiB,QAAQ,KAAK;;;;;;;;;;;;kCAE7C,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,mBAA6B,OAAX,QAAQ,EAAE,EAAC;0CAC1D;;;;;;0CAGD,8UAAC,iLAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;0CAAoB;;;;;;;;;;;;;;;;;;0BAM3D,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC,6KAAI;;0CACH,8UAAC,mLAAU;0CACT,cAAA,8UAAC,kLAAS;8CAAC;;;;;;;;;;;0CAEb,8UAAC,oLAAW;gCAAC,WAAU;;kDACrB,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;;oDAAW,QAAQ,SAAS;oDAAC;oDAAE,QAAQ,QAAQ;;;;;;;;;;;;;kDAE9D,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,KAAK;;;;;;;;;;;;kDAEvC,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,KAAK,IAAI;;;;;;;;;;;;kDAE3C,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,MAAM,GAAG,eAAe,QAAQ,MAAM,IAAI;;;;;;;;;;;;kDAE5E,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW,GAAG,WAAW,QAAQ,WAAW,IAAI;;;;;;;;;;;;kDAG7D,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAMjD,8UAAC,6KAAI;;0CACH,8UAAC,mLAAU;0CACT,cAAA,8UAAC,kLAAS;8CAAC;;;;;;;;;;;0CAEb,8UAAC,oLAAW;gCAAC,WAAU;;kDACrB,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,aAAa,IAAI;;;;;;;;;;;;kDAEnD,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DACV,QAAQ,UAAU,GAAG,AAAC,GAAqB,OAAnB,QAAQ,UAAU,EAAC,YAAU;;;;;;;;;;;;kDAG1D,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW,GAAG,WAAW,QAAQ,WAAW,IAAI;;;;;;;;;;;;kDAG7D,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DACV,QAAQ,MAAM,GAAG,AAAC,IAAmC,OAAhC,QAAQ,MAAM,CAAC,cAAc,MAAO;;;;;;;;;;;;kDAG9D,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAI,WAAU;0DACb,cAAA,8UAAC,+KAAK;oDAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;8DAC5C,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,8UAAC,6KAAI;;0CACH,8UAAC,mLAAU;0CACT,cAAA,8UAAC,kLAAS;8CAAC;;;;;;;;;;;0CAEb,8UAAC,oLAAW;gCAAC,WAAU;;kDACrB,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,IAAI,CAAC,EAAE;;;;;;;;;;;;kDAEzC,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;kDAE3C,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,WAAW,QAAQ,SAAS;;;;;;;;;;;;kDAEtD,8UAAC;;0DACC,8UAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8UAAC;gDAAE,WAAU;0DAAW,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5D,8UAAC;gBAAI,WAAU;;kCACb,8UAAC,6KAAI;;0CACH,8UAAC,mLAAU;0CACT,cAAA,8UAAC,kLAAS;8CAAC;;;;;;;;;;;0CAEb,8UAAC,oLAAW;0CACT,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,kBAC3C,8UAAC;oCAAI,WAAU;8CACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oBACpB,8UAAC,+KAAK;4CAAc,SAAQ;4CAAY,WAAU;;gDAC/C,IAAI,IAAI;gDAAC;gDAAE,IAAI,OAAO,CAAC,IAAI;;2CADlB,IAAI,EAAE;;;;;;;;;yDAMtB,8UAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAKnC,8UAAC,6KAAI;;0CACH,8UAAC,mLAAU;0CACT,cAAA,8UAAC,kLAAS;8CAAC;;;;;;;;;;;0CAEb,8UAAC,oLAAW;0CACT,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,kBAC7C,8UAAC;oCAAI,WAAU;8CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,8UAAC,+KAAK;4CAAkB,SAAQ;4CAAU,WAAU;sDACjD,QAAQ,IAAI;2CADH,QAAQ,EAAE;;;;;;;;;yDAM1B,8UAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GApPwB;;QACP,mSAAS;;;KADF", "debugId": null}}]}