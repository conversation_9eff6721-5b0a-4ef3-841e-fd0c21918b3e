{"/(auth)/login/page": "app/(auth)/login/page.js", "/(dash)/admin/attendance/page": "app/(dash)/admin/attendance/page.js", "/(dash)/admin/exams/page": "app/(dash)/admin/exams/page.js", "/(dash)/admin/page": "app/(dash)/admin/page.js", "/(dash)/admin/reports/page": "app/(dash)/admin/reports/page.js", "/(dash)/admin/settings/page": "app/(dash)/admin/settings/page.js", "/(dash)/admin/students/page": "app/(dash)/admin/students/page.js", "/(dash)/admin/teachers/page": "app/(dash)/admin/teachers/page.js", "/(dash)/student/attendance/page": "app/(dash)/student/attendance/page.js", "/(dash)/student/marks/page": "app/(dash)/student/marks/page.js", "/(dash)/student/page": "app/(dash)/student/page.js", "/api/admin/classes/route": "app/api/admin/classes/route.js", "/api/admin/dashboard/stats/route": "app/api/admin/dashboard/stats/route.js", "/api/admin/exams/route": "app/api/admin/exams/route.js", "/api/admin/reports/route": "app/api/admin/reports/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/teachers/route": "app/api/admin/teachers/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/student/attendance/route": "app/api/student/attendance/route.js", "/api/student/dashboard/stats/route": "app/api/student/dashboard/stats/route.js", "/api/student/marks/route": "app/api/student/marks/route.js", "/api/student/subjects/route": "app/api/student/subjects/route.js", "/api/student/terms/route": "app/api/student/terms/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js"}