{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/student/dashboard/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'STUDENT') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get student record for the logged-in user\n    const student = await prisma.student.findUnique({\n      where: {\n        userId: session.user.id\n      },\n      include: {\n        currentClass: {\n          include: {\n            subjects: true\n          }\n        },\n        currentSection: true\n      }\n    })\n\n    if (!student) {\n      return NextResponse.json({ error: 'Student not found' }, { status: 404 })\n    }\n\n    // Get statistics in parallel\n    const [attendanceStats, marksStats, upcomingExams] = await Promise.all([\n      // Attendance statistics (last 30 days)\n      prisma.attendance.aggregate({\n        where: {\n          studentId: student.id,\n          date: {\n            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n          }\n        },\n        _count: {\n          id: true\n        }\n      }).then(async (total) => {\n        const present = await prisma.attendance.count({\n          where: {\n            studentId: student.id,\n            date: {\n              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n            },\n            status: 'PRESENT'\n          }\n        })\n        return {\n          total: total._count.id,\n          present,\n          rate: total._count.id > 0 ? (present / total._count.id) * 100 : 0\n        }\n      }),\n      \n      // Marks statistics\n      prisma.mark.aggregate({\n        where: {\n          studentId: student.id\n        },\n        _avg: {\n          obtainedMarks: true\n        },\n        _count: {\n          id: true\n        }\n      }),\n      \n      // Upcoming exams for student's class\n      prisma.exam.findMany({\n        where: {\n          subject: {\n            classId: student.currentClassId\n          },\n          date: {\n            gte: new Date() // Future exams\n          }\n        },\n        include: {\n          subject: true\n        },\n        orderBy: {\n          date: 'asc'\n        },\n        take: 5\n      })\n    ])\n\n    // Get recent marks with subject details\n    const recentMarks = await prisma.mark.findMany({\n      where: {\n        studentId: student.id\n      },\n      include: {\n        exam: {\n          include: {\n            subject: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      },\n      take: 10\n    })\n\n    const stats = {\n      currentClass: student.currentClass?.name || 'Not assigned',\n      currentSection: student.currentSection?.name || 'Not assigned',\n      rollNumber: student.rollNumber || 'Not assigned',\n      attendanceRate: Math.round(attendanceStats.rate * 10) / 10,\n      averageMarks: marksStats._avg.obtainedMarks \n        ? Math.round(marksStats._avg.obtainedMarks * 10) / 10 \n        : 0,\n      totalSubjects: student.currentClass?.subjects.length || 0,\n      upcomingExams: upcomingExams.length,\n      totalMarksRecords: marksStats._count.id,\n      totalAttendanceRecords: attendanceStats.total,\n      recentMarks: recentMarks.map(mark => ({\n        id: mark.id,\n        subject: mark.exam.subject.name,\n        examName: mark.exam.name,\n        obtainedMarks: mark.obtainedMarks,\n        maxMarks: mark.exam.maxMarks,\n        percentage: Math.round((mark.obtainedMarks / mark.exam.maxMarks) * 100),\n        date: mark.exam.date\n      })),\n      upcomingExamsList: upcomingExams.map(exam => ({\n        id: exam.id,\n        name: exam.name,\n        subject: exam.subject.name,\n        date: exam.date,\n        maxMarks: exam.maxMarks\n      }))\n    }\n\n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Error fetching student dashboard stats:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,4CAA4C;QAC5C,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACzB;YACA,SAAS;gBACP,cAAc;oBACZ,SAAS;wBACP,UAAU;oBACZ;gBACF;gBACA,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,6BAA6B;QAC7B,MAAM,CAAC,iBAAiB,YAAY,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrE,uCAAuC;YACvC,8JAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,OAAO;oBACL,WAAW,QAAQ,EAAE;oBACrB,MAAM;wBACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,eAAe;oBACtE;gBACF;gBACA,QAAQ;oBACN,IAAI;gBACN;YACF,GAAG,IAAI,CAAC,OAAO;gBACb,MAAM,UAAU,MAAM,8JAAM,CAAC,UAAU,CAAC,KAAK,CAAC;oBAC5C,OAAO;wBACL,WAAW,QAAQ,EAAE;wBACrB,MAAM;4BACJ,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;wBACjD;wBACA,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,OAAO,MAAM,MAAM,CAAC,EAAE;oBACtB;oBACA,MAAM,MAAM,MAAM,CAAC,EAAE,GAAG,IAAI,AAAC,UAAU,MAAM,MAAM,CAAC,EAAE,GAAI,MAAM;gBAClE;YACF;YAEA,mBAAmB;YACnB,8JAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO;oBACL,WAAW,QAAQ,EAAE;gBACvB;gBACA,MAAM;oBACJ,eAAe;gBACjB;gBACA,QAAQ;oBACN,IAAI;gBACN;YACF;YAEA,qCAAqC;YACrC,8JAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB,OAAO;oBACL,SAAS;wBACP,SAAS,QAAQ,cAAc;oBACjC;oBACA,MAAM;wBACJ,KAAK,IAAI,OAAO,eAAe;oBACjC;gBACF;gBACA,SAAS;oBACP,SAAS;gBACX;gBACA,SAAS;oBACP,MAAM;gBACR;gBACA,MAAM;YACR;SACD;QAED,wCAAwC;QACxC,MAAM,cAAc,MAAM,8JAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,WAAW,QAAQ,EAAE;YACvB;YACA,SAAS;gBACP,MAAM;oBACJ,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;QACR;QAEA,MAAM,QAAQ;YACZ,cAAc,QAAQ,YAAY,EAAE,QAAQ;YAC5C,gBAAgB,QAAQ,cAAc,EAAE,QAAQ;YAChD,YAAY,QAAQ,UAAU,IAAI;YAClC,gBAAgB,KAAK,KAAK,CAAC,gBAAgB,IAAI,GAAG,MAAM;YACxD,cAAc,WAAW,IAAI,CAAC,aAAa,GACvC,KAAK,KAAK,CAAC,WAAW,IAAI,CAAC,aAAa,GAAG,MAAM,KACjD;YACJ,eAAe,QAAQ,YAAY,EAAE,SAAS,UAAU;YACxD,eAAe,cAAc,MAAM;YACnC,mBAAmB,WAAW,MAAM,CAAC,EAAE;YACvC,wBAAwB,gBAAgB,KAAK;YAC7C,aAAa,YAAY,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACpC,IAAI,KAAK,EAAE;oBACX,SAAS,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI;oBAC/B,UAAU,KAAK,IAAI,CAAC,IAAI;oBACxB,eAAe,KAAK,aAAa;oBACjC,UAAU,KAAK,IAAI,CAAC,QAAQ;oBAC5B,YAAY,KAAK,KAAK,CAAC,AAAC,KAAK,aAAa,GAAG,KAAK,IAAI,CAAC,QAAQ,GAAI;oBACnE,MAAM,KAAK,IAAI,CAAC,IAAI;gBACtB,CAAC;YACD,mBAAmB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC5C,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO,CAAC,IAAI;oBAC1B,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;gBACzB,CAAC;QACH;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}