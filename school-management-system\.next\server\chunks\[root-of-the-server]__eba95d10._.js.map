{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/settings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\r\nimport { getServerSession } from 'next-auth'\r\nimport { authOptions } from '@/lib/auth'\r\nimport { prisma } from '@/lib/db'\r\n\r\n// Small helper to build the list of settings to upsert. Exported for unit tests.\r\nexport function buildSettingsUpdates(type: string, data: Record<string, unknown>): Array<{ key: string; value: string }> {\r\n  const settingsToUpdate: Array<{ key: string; value: string }> = []\r\n\r\n  switch (type) {\r\n    case 'general':\r\n      settingsToUpdate.push(\r\n        { key: 'school_name', value: data.name },\r\n        { key: 'school_address', value: data.address },\r\n        { key: 'school_phone', value: data.phone },\r\n        { key: 'school_email', value: data.email },\r\n        { key: 'school_website', value: data.website },\r\n        { key: 'school_principal', value: data.principal },\r\n        { key: 'school_established_year', value: data.establishedYear }\r\n      )\r\n      break\r\n\r\n    case 'academic':\r\n      settingsToUpdate.push(\r\n        { key: 'academic_year', value: data.academicYear },\r\n        { key: 'current_term', value: data.currentTerm },\r\n        { key: 'grading_system', value: data.gradingSystem },\r\n        { key: 'pass_percentage', value: data.passPercentage?.toString() },\r\n        { key: 'max_attendance_percentage', value: data.maxAttendancePercentage?.toString() }\r\n      )\r\n      break\r\n\r\n    case 'notifications':\r\n      settingsToUpdate.push(\r\n        { key: 'attendance_alerts', value: data.attendanceAlerts?.toString() },\r\n        { key: 'exam_results', value: data.examResults?.toString() },\r\n        { key: 'report_card_generation', value: data.reportCardGeneration?.toString() },\r\n        { key: 'system_updates', value: data.systemUpdates?.toString() }\r\n      )\r\n      break\r\n\r\n    case 'security':\r\n      settingsToUpdate.push(\r\n        { key: 'session_timeout', value: data.sessionTimeout?.toString() },\r\n        { key: 'password_policy', value: data.passwordPolicy },\r\n        { key: 'two_factor_auth', value: data.twoFactorAuth?.toString() },\r\n        { key: 'login_attempts', value: data.loginAttempts?.toString() }\r\n      )\r\n      break\r\n\r\n    default:\r\n      throw new Error('Invalid settings type')\r\n  }\r\n\r\n  // Ensure no undefined slips through which would cause Prisma validation error\r\n  for (const s of settingsToUpdate) {\r\n    if (s.value === undefined) {\r\n      throw new Error(`Missing value for setting key: ${s.key}`)\r\n    }\r\n  }\r\n\r\n  return settingsToUpdate\r\n}\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n\r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const { searchParams } = new URL(request.url)\r\n    const type = searchParams.get('type')\r\n\r\n    // Get all settings\r\n    const settings = await prisma.setting.findMany()\r\n\r\n    // Convert settings array to object for easier access\r\n    const settingsObject = settings.reduce((acc, setting) => {\r\n      acc[setting.key] = setting.value\r\n      return acc\r\n    }, {} as Record<string, string>)\r\n\r\n    // Return different data based on type parameter\r\n    switch (type) {\r\n      case 'general':\r\n        return NextResponse.json({\r\n          schoolName: settingsObject['school_name'] || 'Advance School',\r\n          address: settingsObject['school_address'] || '123 Education Street, City, State 12345',\r\n          phone: settingsObject['school_phone'] || '+****************',\r\n          email: settingsObject['school_email'] || '<EMAIL>',\r\n          website: settingsObject['school_website'] || 'www.advanceschool.edu',\r\n          principal: settingsObject['school_principal'] || 'Dr. John Smith',\r\n          establishedYear: settingsObject['school_established_year'] || '1995'\r\n        })\r\n\r\n      case 'academic':\r\n        return NextResponse.json({\r\n          academicYear: settingsObject['academic_year'] || '2024-2025',\r\n          currentTerm: settingsObject['current_term'] || 'Term 1',\r\n          gradingSystem: settingsObject['grading_system'] || 'LETTER',\r\n          passPercentage: parseInt(settingsObject['pass_percentage']) || 40,\r\n          maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75\r\n        })\r\n\r\n      case 'notifications':\r\n        return NextResponse.json({\r\n          attendanceAlerts: settingsObject['attendance_alerts'] === 'true',\r\n          examResults: settingsObject['exam_results'] === 'true',\r\n          reportCardGeneration: settingsObject['report_card_generation'] === 'true',\r\n          systemUpdates: settingsObject['system_updates'] === 'true'\r\n        })\r\n\r\n      case 'security':\r\n        return NextResponse.json({\r\n          sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,\r\n          passwordPolicy: settingsObject['password_policy'] || 'strong',\r\n          twoFactorAuth: settingsObject['two_factor_auth'] === 'true',\r\n          loginAttempts: settingsObject['login_attempts'] === 'true'\r\n        })\r\n\r\n      default:\r\n        // Return all settings\r\n        return NextResponse.json({\r\n          general: {\r\n            schoolName: settingsObject['school_name'] || 'Advance School',\r\n            address: settingsObject['school_address'] || '123 Education Street, City, State 12345',\r\n            phone: settingsObject['school_phone'] || '+****************',\r\n            email: settingsObject['school_email'] || '<EMAIL>',\r\n            website: settingsObject['school_website'] || 'www.advanceschool.edu',\r\n            principal: settingsObject['school_principal'] || 'Dr. John Smith',\r\n            establishedYear: settingsObject['school_established_year'] || '1995'\r\n          },\r\n          academic: {\r\n            academicYear: settingsObject['academic_year'] || '2024-2025',\r\n            currentTerm: settingsObject['current_term'] || 'Term 1',\r\n            gradingSystem: settingsObject['grading_system'] || 'LETTER',\r\n            passPercentage: parseInt(settingsObject['pass_percentage']) || 40,\r\n            maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75\r\n          },\r\n          notifications: {\r\n            attendanceAlerts: settingsObject['attendance_alerts'] === 'true',\r\n            examResults: settingsObject['exam_results'] === 'true',\r\n            reportCardGeneration: settingsObject['report_card_generation'] === 'true',\r\n            systemUpdates: settingsObject['system_updates'] === 'true'\r\n          },\r\n          security: {\r\n            sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,\r\n            passwordPolicy: settingsObject['password_policy'] || 'strong',\r\n            twoFactorAuth: settingsObject['two_factor_auth'] === 'true',\r\n            loginAttempts: settingsObject['login_attempts'] === 'true'\r\n          }\r\n        })\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching settings:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n\r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const body = await request.json()\r\n    const { type, data } = body\r\n\r\n    let settingsToUpdate: Array<{ key: string; value: string }>\r\n    try {\r\n      settingsToUpdate = buildSettingsUpdates(type, data)\r\n    } catch (e) {\r\n      return NextResponse.json({ error: e instanceof Error ? e.message : 'Invalid payload' }, { status: 400 })\r\n    }\r\n\r\n    // Update or create settings\r\n    for (const setting of settingsToUpdate) {\r\n      await prisma.setting.upsert({\r\n        where: { key: setting.key },\r\n        update: { value: setting.value },\r\n        create: {\r\n          key: setting.key,\r\n          value: setting.value,\r\n        }\r\n      })\r\n    }\r\n\r\n    // Log the settings update\r\n    await prisma.auditLog.create({\r\n      data: {\r\n        action: 'SETTINGS_UPDATE',\r\n        entity: 'SETTING',\r\n        entityId: type,\r\n        userId: session.user.id,\r\n        meta: {\r\n          details: `Updated ${type} settings`,\r\n          ipAddress: request.headers.get('x-forwarded-for') || 'unknown'\r\n        }\r\n      }\r\n    })\r\n\r\n    return NextResponse.json({\r\n      message: `${type} settings updated successfully`,\r\n      type,\r\n      data\r\n    })\r\n  } catch (error) {\r\n    console.error('Error updating settings:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAGO,SAAS,qBAAqB,IAAY,EAAE,IAA6B;IAC9E,MAAM,mBAA0D,EAAE;IAElE,OAAQ;QACN,KAAK;YACH,iBAAiB,IAAI,CACnB;gBAAE,KAAK;gBAAe,OAAO,KAAK,IAAI;YAAC,GACvC;gBAAE,KAAK;gBAAkB,OAAO,KAAK,OAAO;YAAC,GAC7C;gBAAE,KAAK;gBAAgB,OAAO,KAAK,KAAK;YAAC,GACzC;gBAAE,KAAK;gBAAgB,OAAO,KAAK,KAAK;YAAC,GACzC;gBAAE,KAAK;gBAAkB,OAAO,KAAK,OAAO;YAAC,GAC7C;gBAAE,KAAK;gBAAoB,OAAO,KAAK,SAAS;YAAC,GACjD;gBAAE,KAAK;gBAA2B,OAAO,KAAK,eAAe;YAAC;YAEhE;QAEF,KAAK;YACH,iBAAiB,IAAI,CACnB;gBAAE,KAAK;gBAAiB,OAAO,KAAK,YAAY;YAAC,GACjD;gBAAE,KAAK;gBAAgB,OAAO,KAAK,WAAW;YAAC,GAC/C;gBAAE,KAAK;gBAAkB,OAAO,KAAK,aAAa;YAAC,GACnD;gBAAE,KAAK;gBAAmB,OAAO,KAAK,cAAc,EAAE;YAAW,GACjE;gBAAE,KAAK;gBAA6B,OAAO,KAAK,uBAAuB,EAAE;YAAW;YAEtF;QAEF,KAAK;YACH,iBAAiB,IAAI,CACnB;gBAAE,KAAK;gBAAqB,OAAO,KAAK,gBAAgB,EAAE;YAAW,GACrE;gBAAE,KAAK;gBAAgB,OAAO,KAAK,WAAW,EAAE;YAAW,GAC3D;gBAAE,KAAK;gBAA0B,OAAO,KAAK,oBAAoB,EAAE;YAAW,GAC9E;gBAAE,KAAK;gBAAkB,OAAO,KAAK,aAAa,EAAE;YAAW;YAEjE;QAEF,KAAK;YACH,iBAAiB,IAAI,CACnB;gBAAE,KAAK;gBAAmB,OAAO,KAAK,cAAc,EAAE;YAAW,GACjE;gBAAE,KAAK;gBAAmB,OAAO,KAAK,cAAc;YAAC,GACrD;gBAAE,KAAK;gBAAmB,OAAO,KAAK,aAAa,EAAE;YAAW,GAChE;gBAAE,KAAK;gBAAkB,OAAO,KAAK,aAAa,EAAE;YAAW;YAEjE;QAEF;YACE,MAAM,IAAI,MAAM;IACpB;IAEA,8EAA8E;IAC9E,KAAK,MAAM,KAAK,iBAAkB;QAChC,IAAI,EAAE,KAAK,KAAK,WAAW;YACzB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,EAAE,GAAG,EAAE;QAC3D;IACF;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,mBAAmB;QACnB,MAAM,WAAW,MAAM,8JAAM,CAAC,OAAO,CAAC,QAAQ;QAE9C,qDAAqD;QACrD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK;YAC3C,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,QAAQ,KAAK;YAChC,OAAO;QACT,GAAG,CAAC;QAEJ,gDAAgD;QAChD,OAAQ;YACN,KAAK;gBACH,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,YAAY,cAAc,CAAC,cAAc,IAAI;oBAC7C,SAAS,cAAc,CAAC,iBAAiB,IAAI;oBAC7C,OAAO,cAAc,CAAC,eAAe,IAAI;oBACzC,OAAO,cAAc,CAAC,eAAe,IAAI;oBACzC,SAAS,cAAc,CAAC,iBAAiB,IAAI;oBAC7C,WAAW,cAAc,CAAC,mBAAmB,IAAI;oBACjD,iBAAiB,cAAc,CAAC,0BAA0B,IAAI;gBAChE;YAEF,KAAK;gBACH,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,cAAc,cAAc,CAAC,gBAAgB,IAAI;oBACjD,aAAa,cAAc,CAAC,eAAe,IAAI;oBAC/C,eAAe,cAAc,CAAC,iBAAiB,IAAI;oBACnD,gBAAgB,SAAS,cAAc,CAAC,kBAAkB,KAAK;oBAC/D,yBAAyB,SAAS,cAAc,CAAC,4BAA4B,KAAK;gBACpF;YAEF,KAAK;gBACH,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,kBAAkB,cAAc,CAAC,oBAAoB,KAAK;oBAC1D,aAAa,cAAc,CAAC,eAAe,KAAK;oBAChD,sBAAsB,cAAc,CAAC,yBAAyB,KAAK;oBACnE,eAAe,cAAc,CAAC,iBAAiB,KAAK;gBACtD;YAEF,KAAK;gBACH,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,gBAAgB,SAAS,cAAc,CAAC,kBAAkB,KAAK;oBAC/D,gBAAgB,cAAc,CAAC,kBAAkB,IAAI;oBACrD,eAAe,cAAc,CAAC,kBAAkB,KAAK;oBACrD,eAAe,cAAc,CAAC,iBAAiB,KAAK;gBACtD;YAEF;gBACE,sBAAsB;gBACtB,OAAO,iSAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;wBACP,YAAY,cAAc,CAAC,cAAc,IAAI;wBAC7C,SAAS,cAAc,CAAC,iBAAiB,IAAI;wBAC7C,OAAO,cAAc,CAAC,eAAe,IAAI;wBACzC,OAAO,cAAc,CAAC,eAAe,IAAI;wBACzC,SAAS,cAAc,CAAC,iBAAiB,IAAI;wBAC7C,WAAW,cAAc,CAAC,mBAAmB,IAAI;wBACjD,iBAAiB,cAAc,CAAC,0BAA0B,IAAI;oBAChE;oBACA,UAAU;wBACR,cAAc,cAAc,CAAC,gBAAgB,IAAI;wBACjD,aAAa,cAAc,CAAC,eAAe,IAAI;wBAC/C,eAAe,cAAc,CAAC,iBAAiB,IAAI;wBACnD,gBAAgB,SAAS,cAAc,CAAC,kBAAkB,KAAK;wBAC/D,yBAAyB,SAAS,cAAc,CAAC,4BAA4B,KAAK;oBACpF;oBACA,eAAe;wBACb,kBAAkB,cAAc,CAAC,oBAAoB,KAAK;wBAC1D,aAAa,cAAc,CAAC,eAAe,KAAK;wBAChD,sBAAsB,cAAc,CAAC,yBAAyB,KAAK;wBACnE,eAAe,cAAc,CAAC,iBAAiB,KAAK;oBACtD;oBACA,UAAU;wBACR,gBAAgB,SAAS,cAAc,CAAC,kBAAkB,KAAK;wBAC/D,gBAAgB,cAAc,CAAC,kBAAkB,IAAI;wBACrD,eAAe,cAAc,CAAC,kBAAkB,KAAK;wBACrD,eAAe,cAAc,CAAC,iBAAiB,KAAK;oBACtD;gBACF;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;QAEvB,IAAI;QACJ,IAAI;YACF,mBAAmB,qBAAqB,MAAM;QAChD,EAAE,OAAO,GAAG;YACV,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,aAAa,QAAQ,EAAE,OAAO,GAAG;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACxG;QAEA,4BAA4B;QAC5B,KAAK,MAAM,WAAW,iBAAkB;YACtC,MAAM,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC1B,OAAO;oBAAE,KAAK,QAAQ,GAAG;gBAAC;gBAC1B,QAAQ;oBAAE,OAAO,QAAQ,KAAK;gBAAC;gBAC/B,QAAQ;oBACN,KAAK,QAAQ,GAAG;oBAChB,OAAO,QAAQ,KAAK;gBACtB;YACF;QACF;QAEA,0BAA0B;QAC1B,MAAM,8JAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,MAAM;gBACJ,QAAQ;gBACR,QAAQ;gBACR,UAAU;gBACV,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,MAAM;oBACJ,SAAS,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC;oBACnC,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;gBACvD;YACF;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS,GAAG,KAAK,8BAA8B,CAAC;YAChD;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}