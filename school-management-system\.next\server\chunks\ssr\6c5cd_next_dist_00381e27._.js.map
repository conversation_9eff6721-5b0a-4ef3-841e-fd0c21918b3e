{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/page-path/normalize-path-sep.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is no backslash\n * escaping slashes in the path. Example:\n *  - `foo\\/bar\\/baz` -> `foo/bar/baz`\n */\nexport function normalizePathSep(path: string): string {\n  return path.replace(/\\\\/g, '/')\n}\n"], "names": ["normalizePathSep", "path", "replace"], "mappings": "AAAA;;;;CAIC;;;+BACeA,oBAAAA;;;eAAAA;;;AAAT,SAASA,iBAAiBC,IAAY;IAC3C,OAAOA,KAAKC,OAAO,CAAC,OAAO;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/isomorphic/path.js"], "sourcesContent": ["/**\n * This module is for next.js server internal usage of path module.\n * It will use native path module for nodejs runtime.\n * It will use path-browserify polyfill for edge runtime.\n */\nlet path\n\nif (process.env.NEXT_RUNTIME === 'edge') {\n  path = require('next/dist/compiled/path-browserify')\n} else {\n  path = require('path')\n}\n\nmodule.exports = path\n"], "names": ["path", "process", "env", "NEXT_RUNTIME", "require", "module", "exports"], "mappings": "AAAA;;;;CAIC,GACD,IAAIA;AAEJ,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACLH,OAAOI,QAAQ;AACjB;AAEAC,OAAOC,OAAO,GAAGN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n"], "names": ["normalizeLocalePath", "cache", "WeakMap", "pathname", "locales", "lowercasedLocales", "get", "map", "locale", "toLowerCase", "set", "detectedLocale", "segments", "split", "segment", "index", "indexOf", "slice", "length"], "mappings": ";;;+BAqBgBA,uBAAAA;;;eAAAA;;;AAhBhB;;;;CAIC,GACD,MAAMC,QAAQ,IAAIC;AAWX,SAASF,oBACdG,QAAgB,EAChBC,OAA2B;IAE3B,sDAAsD;IACtD,IAAI,CAACA,SAAS,OAAO;QAAED;IAAS;IAEhC,iEAAiE;IACjE,IAAIE,oBAAoBJ,MAAMK,GAAG,CAACF;IAClC,IAAI,CAACC,mBAAmB;QACtBA,oBAAoBD,QAAQG,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QAC9DR,MAAMS,GAAG,CAACN,SAASC;IACrB;IAEA,IAAIM;IAEJ,oEAAoE;IACpE,yEAAyE;IACzE,MAAMC,WAAWT,SAASU,KAAK,CAAC,KAAK;IAErC,0EAA0E;IAC1E,UAAU;IACV,IAAI,CAACD,QAAQ,CAAC,EAAE,EAAE,OAAO;QAAET;IAAS;IAEpC,0DAA0D;IAC1D,MAAMW,UAAUF,QAAQ,CAAC,EAAE,CAACH,WAAW;IAEvC,yEAAyE;IACzE,mCAAmC;IACnC,MAAMM,QAAQV,kBAAkBW,OAAO,CAACF;IACxC,IAAIC,QAAQ,GAAG,OAAO;QAAEZ;IAAS;IAEjC,oCAAoC;IACpCQ,iBAAiBP,OAAO,CAACW,MAAM;IAE/B,gDAAgD;IAChDZ,WAAWA,SAASc,KAAK,CAACN,eAAeO,MAAM,GAAG,MAAM;IAExD,OAAO;QAAEf;QAAUQ;IAAe;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/path-match.ts"], "sourcesContent": ["import type { Key } from 'next/dist/compiled/path-to-regexp'\nimport { regexpToFunction } from 'next/dist/compiled/path-to-regexp'\nimport { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\n\ninterface Options {\n  /**\n   * A transformer function that will be applied to the regexp generated\n   * from the provided path and path-to-regexp.\n   */\n  regexModifier?: (regex: string) => string\n  /**\n   * When true the function will remove all unnamed parameters\n   * from the matched parameters.\n   */\n  removeUnnamedParams?: boolean\n  /**\n   * When true the regexp won't allow an optional trailing delimiter\n   * to match.\n   */\n  strict?: boolean\n\n  /**\n   * When true the matcher will be case-sensitive, defaults to false\n   */\n  sensitive?: boolean\n}\n\nexport type PatchMatcher = (\n  pathname: string,\n  params?: Record<string, any>\n) => Record<string, any> | false\n\n/**\n * Generates a path matcher function for a given path and options based on\n * path-to-regexp. By default the match will be case insensitive, non strict\n * and delimited by `/`.\n */\nexport function getPathMatch(path: string, options?: Options): PatchMatcher {\n  const keys: Key[] = []\n  const regexp = pathToRegexp(path, keys, {\n    delimiter: '/',\n    sensitive:\n      typeof options?.sensitive === 'boolean' ? options.sensitive : false,\n    strict: options?.strict,\n  })\n\n  const matcher = regexpToFunction<Record<string, any>>(\n    options?.regexModifier\n      ? new RegExp(options.regexModifier(regexp.source), regexp.flags)\n      : regexp,\n    keys\n  )\n\n  /**\n   * A matcher function that will check if a given pathname matches the path\n   * given in the builder function. When the path does not match it will return\n   * `false` but if it does it will return an object with the matched params\n   * merged with the params provided in the second argument.\n   */\n  return (pathname, params) => {\n    // If no pathname is provided it's not a match.\n    if (typeof pathname !== 'string') return false\n\n    const match = matcher(pathname)\n\n    // If the path did not match `false` will be returned.\n    if (!match) return false\n\n    /**\n     * If unnamed params are not allowed they must be removed from\n     * the matched parameters. path-to-regexp uses \"string\" for named and\n     * \"number\" for unnamed parameters.\n     */\n    if (options?.removeUnnamedParams) {\n      for (const key of keys) {\n        if (typeof key.name === 'number') {\n          delete match.params[key.name]\n        }\n      }\n    }\n\n    return { ...params, ...match.params }\n  }\n}\n"], "names": ["getPathMatch", "path", "options", "keys", "regexp", "pathToRegexp", "delimiter", "sensitive", "strict", "matcher", "regexpToFunction", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": ";;;+BAqCgBA,gBAAAA;;;eAAAA;;;8BApCiB;AAoC1B,SAASA,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASC,CAAAA,GAAAA,cAAAA,YAAY,EAACJ,MAAME,MAAM;QACtCG,WAAW;QACXC,WACE,OAAA,CAAOL,WAAAA,OAAAA,KAAAA,IAAAA,QAASK,SAAS,MAAK,YAAYL,QAAQK,SAAS,GAAG;QAChEC,MAAM,EAAEN,WAAAA,OAAAA,KAAAA,IAAAA,QAASM,MAAM;IACzB;IAEA,MAAMC,UAAUC,CAAAA,GAAAA,cAAAA,gBAAgB,EAC9BR,CAAAA,WAAAA,OAAAA,KAAAA,IAAAA,QAASS,aAAa,IAClB,IAAIC,OAAOV,QAAQS,aAAa,CAACP,OAAOS,MAAM,GAAGT,OAAOU,KAAK,IAC7DV,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACY,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQR,QAAQM;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIf,WAAAA,OAAAA,KAAAA,IAAAA,QAASgB,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOhB,KAAM;gBACtB,IAAI,OAAOgB,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BV,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BV,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACP,iCAA8BV,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIJ,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/escape-regexp.ts"], "sourcesContent": ["// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n"], "names": ["escapeStringRegexp", "reHasRegExp", "reReplaceRegExp", "str", "test", "replace"], "mappings": "AAAA,0EAA0E;;;;+BAI1DA,sBAAAA;;;eAAAA;;;AAHhB,MAAMC,cAAc;AACpB,MAAMC,kBAAkB;AAEjB,SAASF,mBAAmBG,GAAW;IAC5C,+GAA+G;IAC/G,IAAIF,YAAYG,IAAI,CAACD,MAAM;QACzB,OAAOA,IAAIE,OAAO,CAACH,iBAAiB;IACtC;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/get-dynamic-param.ts"], "sourcesContent": ["import type { DynamicParam } from '../../../../server/app-render/app-render'\nimport type { DynamicParamTypesShort } from '../../../../server/app-render/types'\nimport type { FallbackRouteParams } from '../../../../server/request/fallback-params'\n\n/**\n *\n * Shared logic on client and server for creating a dynamic param value.\n *\n * This code needs to be shared with the client so it can extract dynamic route\n * params from the URL without a server request.\n *\n * Because everything in this module is sent to the client, we should aim to\n * keep this code as simple as possible. The special case handling for catchall\n * and optional is, alas, unfortunate.\n */\nexport function getDynamicParam(\n  params: { [key: string]: any },\n  segmentKey: string,\n  dynamicParamType: DynamicParamTypesShort,\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): DynamicParam {\n  let value = params[segmentKey]\n\n  if (fallbackRouteParams && fallbackRouteParams.has(segmentKey)) {\n    value = fallbackRouteParams.get(segmentKey)\n  } else if (Array.isArray(value)) {\n    value = value.map((i) => encodeURIComponent(i))\n  } else if (typeof value === 'string') {\n    value = encodeURIComponent(value)\n  }\n\n  if (!value) {\n    const isCatchall = dynamicParamType === 'c'\n    const isOptionalCatchall = dynamicParamType === 'oc'\n\n    if (isCatchall || isOptionalCatchall) {\n      // handle the case where an optional catchall does not have a value,\n      // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n      if (isOptionalCatchall) {\n        return {\n          param: segmentKey,\n          value: null,\n          type: dynamicParamType,\n          treeSegment: [segmentKey, '', dynamicParamType],\n        }\n      }\n\n      // handle the case where a catchall or optional catchall does not have a value,\n      // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n      value = pagePath\n        .split('/')\n        // remove the first empty string\n        .slice(1)\n        // replace any dynamic params with the actual values\n        .flatMap((pathSegment) => {\n          const param = parseParameter(pathSegment)\n          // if the segment matches a param, return the param value\n          // otherwise, it's a static segment, so just return that\n          return params[param.key] ?? param.key\n        })\n\n      return {\n        param: segmentKey,\n        value,\n        type: dynamicParamType,\n        // This value always has to be a string.\n        treeSegment: [segmentKey, value.join('/'), dynamicParamType],\n      }\n    }\n  }\n\n  return {\n    param: segmentKey,\n    // The value that is passed to user code.\n    value: value,\n    // The value that is rendered in the router tree.\n    treeSegment: [\n      segmentKey,\n      Array.isArray(value) ? value.join('/') : value,\n      dynamicParamType,\n    ],\n    type: dynamicParamType,\n  }\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nexport const PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n"], "names": ["PARAMETER_PATTERN", "getDynamicParam", "parseMatchedParameter", "parseParameter", "params", "segmentKey", "dynamicParamType", "pagePath", "fallbackRouteParams", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "isOptionalCatchall", "param", "type", "treeSegment", "split", "slice", "flatMap", "pathSegment", "key", "join", "match", "optional", "startsWith", "endsWith", "repeat"], "mappings": ";;;;;;;;;;;;;;;;IA+FaA,iBAAiB,EAAA;eAAjBA;;IAhFGC,eAAe,EAAA;eAAfA;;IAmHAC,qBAAqB,EAAA;eAArBA;;IArBAC,cAAc,EAAA;eAAdA;;;AA9FT,SAASF,gBACdG,MAA8B,EAC9BC,UAAkB,EAClBC,gBAAwC,EACxCC,QAAgB,EAChBC,mBAA+C;IAE/C,IAAIC,QAAQL,MAAM,CAACC,WAAW;IAE9B,IAAIG,uBAAuBA,oBAAoBE,GAAG,CAACL,aAAa;QAC9DI,QAAQD,oBAAoBG,GAAG,CAACN;IAClC,OAAO,IAAIO,MAAMC,OAAO,CAACJ,QAAQ;QAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;IAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;QACpCA,QAAQO,mBAAmBP;IAC7B;IAEA,IAAI,CAACA,OAAO;QACV,MAAMQ,aAAaX,qBAAqB;QACxC,MAAMY,qBAAqBZ,qBAAqB;QAEhD,IAAIW,cAAcC,oBAAoB;YACpC,oEAAoE;YACpE,6DAA6D;YAC7D,IAAIA,oBAAoB;gBACtB,OAAO;oBACLC,OAAOd;oBACPI,OAAO;oBACPW,MAAMd;oBACNe,aAAa;wBAAChB;wBAAY;wBAAIC;qBAAiB;gBACjD;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxFG,QAAQF,SACLe,KAAK,CAAC,KACP,gCAAgC;aAC/BC,KAAK,CAAC,GACP,oDAAoD;aACnDC,OAAO,CAAC,CAACC;gBACR,MAAMN,QAAQhB,eAAesB;oBAGtBrB;gBAFP,yDAAyD;gBACzD,wDAAwD;gBACxD,OAAOA,CAAAA,oBAAAA,MAAM,CAACe,MAAMO,GAAG,CAAC,KAAA,OAAjBtB,oBAAqBe,MAAMO,GAAG;YACvC;YAEF,OAAO;gBACLP,OAAOd;gBACPI;gBACAW,MAAMd;gBACN,wCAAwC;gBACxCe,aAAa;oBAAChB;oBAAYI,MAAMkB,IAAI,CAAC;oBAAMrB;iBAAiB;YAC9D;QACF;IACF;IAEA,OAAO;QACLa,OAAOd;QACP,yCAAyC;QACzCI,OAAOA;QACP,iDAAiD;QACjDY,aAAa;YACXhB;YACAO,MAAMC,OAAO,CAACJ,SAASA,MAAMkB,IAAI,CAAC,OAAOlB;YACzCH;SACD;QACDc,MAAMd;IACR;AACF;AAWO,MAAMN,oBAAoB;AAc1B,SAASG,eAAegB,KAAa;IAC1C,MAAMS,QAAQT,MAAMS,KAAK,CAAC5B;IAE1B,IAAI,CAAC4B,OAAO;QACV,OAAO1B,sBAAsBiB;IAC/B;IAEA,OAAOjB,sBAAsB0B,KAAK,CAAC,EAAE;AACvC;AAaO,SAAS1B,sBAAsBiB,KAAa;IACjD,MAAMU,WAAWV,MAAMW,UAAU,CAAC,QAAQX,MAAMY,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZV,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMS,SAASb,MAAMW,UAAU,CAAC;IAChC,IAAIE,QAAQ;QACVb,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEG,KAAKP;QAAOa;QAAQH;IAAS;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { PARAMETER_PATTERN, parseMatchedParameter } from './get-dynamic-param'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["getNamedMiddlewareRegex", "getNamedRouteRegex", "getRouteRegex", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "removeTrailingSlash", "slice", "split", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "paramMatch<PERSON>", "match", "PARAMETER_PATTERN", "key", "optional", "repeat", "parseMatchedParameter", "pos", "push", "escapeStringRegexp", "s", "substring", "parameterizedRoute", "join", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "NEXT_INTERCEPTION_MARKER_PREFIX", "undefined", "NEXT_QUERY_PARAM_PREFIX", "namedParameterizedRoute", "options", "result", "namedRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;IAiWgBA,uBAAuB,EAAA;eAAvBA;;IA5BAC,kBAAkB,EAAA;eAAlBA;;IA7LAC,aAAa,EAAA;eAAbA;;;2BArIT;oCACoC;8BACR;qCACC;iCACqB;AAyEzD,SAASC,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMC,cAAcC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IACnDP,QAAQQ,UAAU,CAACD;QAErB,MAAME,eAAeT,QAAQU,KAAK,CAACC,iBAAAA,iBAAiB,EAAE,uBAAuB;;QAE7E,IAAIP,eAAeK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEG,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YACpDd,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACd,eAAa;QACpD,OAAO,IAAIK,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEG,GAAG,EAAEE,MAAM,EAAED,QAAQ,EAAE,GAAGE,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACN,YAAY,CAAC,EAAE;YACvEZ,MAAM,CAACe,IAAI,GAAG;gBAAEI,KAAKlB;gBAAcgB;gBAAQD;YAAS;YAEpD,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIL,SAAUD,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIjB,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLY,oBAAoBtB,SAASuB,IAAI,CAAC;QAClCzB;IACF;AACF;AAOO,SAASL,cACd+B,eAAuB,EACvB,KAAA;IAAA,IAAA,EACE5B,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrB4B,+BAA+B,KAAK,EACf,GAJvB,UAAA,KAAA,IAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEH,kBAAkB,EAAExB,MAAM,EAAE,GAAGJ,qBACrC8B,iBACA5B,eACAC;IAGF,IAAI6B,KAAKJ;IACT,IAAI,CAACG,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAQ,MAAGD,KAAG;QACtB5B,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAAS8B;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAOF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAEJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACfrC,OAAO,EACPsC,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAE5B,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,iBAAAA,qBAAqB,EAACf;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIyC,aAAa7B,IAAI8B,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAc,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWvC,KAAK,CAAC,GAAG,MAAM;QAC5CyC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAI,KAAEF,YAAY3B;IACzC,OAAO;QACL0B,SAAS,CAACG,WAAW,GAAG7B;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAMoC,qBAAqBZ,qBACvBlB,CAAAA,GAAAA,cAAAA,kBAAkB,EAACkB,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAW,SAAMR,aAAW;IAC9B,OAAO,IAAI3B,QAAQ;QACjBmC,UAAW,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAW,QAAKR,aAAW;IAC7B;IAEA,OAAO5B,WACF,SAAMmC,qBAAqBC,UAAQ,OACnC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACPxD,KAAa,EACbyD,eAAwB,EACxBxD,aAAsB,EACtBC,aAAsB,EACtB4C,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAMvC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,WAAWC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACP,OAAOQ,KAAK,CAAC,GAAGC,KAAK,CAAC,KAAM;QACpE,MAAMiD,wBAAwB/C,oBAAAA,0BAA0B,CAACgD,IAAI,CAAC,CAAC9C,IAC7DP,QAAQQ,UAAU,CAACD;QAGrB,MAAME,eAAeT,QAAQU,KAAK,CAACC,iBAAAA,iBAAiB,EAAE,uBAAuB;;QAE7E,IAAIyC,yBAAyB3C,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DV,SAASkB,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoB3B,YAAY,CAAC,EAAE;gBACnCT,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBACPG,WAAAA,+BAA+B,GAC/BC;gBACJf;YACF;QAEJ,OAAO,IAAI/B,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIb,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCV,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIU,IAAIgB,sBAAsB;gBAC5BE;gBACArC,SAASS,YAAY,CAAC,EAAE;gBACxB6B;gBACAC,WAAWY,kBAAkBK,WAAAA,uBAAuB,GAAGD;gBACvDf;YACF;YAEA,8DAA8D;YAC9D,IAAI5C,iBAAiBa,YAAY,CAAC,EAAE,EAAE;gBACpCU,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEArB,SAASkB,IAAI,CAACE;QAChB,OAAO;YACLpB,SAASkB,IAAI,CAAE,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAAClB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBc,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDV,SAASkB,IAAI,CAACC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACT,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLgD,yBAAyB1D,SAASuB,IAAI,CAAC;QACvCgB;IACF;AACF;AAUO,SAAS/C,mBACdgC,eAAuB,EACvBmC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAAST,0BACb3B,iBACAmC,QAAQP,eAAe,EACvBO,CAAAA,yBAAAA,QAAQ/D,aAAa,KAAA,OAArB+D,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQ9D,aAAa,KAAA,OAArB8D,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQlB,0BAA0B,KAAA,OAAlCkB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOF,uBAAuB;IAC/C,IAAI,CAACC,QAAQlC,4BAA4B,EAAE;QACzCoC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGpE,cAAc+B,iBAAiBmC,QAAQ;QAC1CE,YAAa,MAAGA,aAAW;QAC3BtB,WAAWqB,OAAOrB,SAAS;IAC7B;AACF;AAMO,SAAShD,wBACdiC,eAAuB,EACvBmC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAG5B,qBAC7B8B,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGH;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAIyC,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLD,YAAa,OAAIE,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEL,uBAAuB,EAAE,GAAGP,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLD,YAAa,MAAGH,0BAA0BM,uBAAqB;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoaaA,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/route-match-utils.ts"], "sourcesContent": ["/**\n * Client-safe utilities for route matching that don't import server-side\n * utilities to avoid bundling issues with Turbopack\n */\n\nimport type {\n  Key,\n  TokensToRegexpOptions,\n  ParseOptions,\n  TokensToFunctionOptions,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  pathToRegexp,\n  compile,\n  regexpToFunction,\n} from 'next/dist/compiled/path-to-regexp'\nimport {\n  hasAdjacentParameterIssues,\n  normalizeAdjacentParameters,\n  stripParameterSeparators,\n} from '../../../../lib/route-pattern-normalizer'\n\n/**\n * Client-safe wrapper around pathToRegexp that handles path-to-regexp 6.3.0+ validation errors.\n * This includes both \"Can not repeat without prefix/suffix\" and \"Must have text between parameters\" errors.\n */\nexport function safePathToRegexp(\n  route: string | RegExp | Array<string | RegExp>,\n  keys?: Key[],\n  options?: TokensToRegexpOptions & ParseOptions\n): RegExp {\n  if (typeof route !== 'string') {\n    return pathToRegexp(route, keys, options)\n  }\n\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return pathToRegexp(routeToUse, keys, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return pathToRegexp(normalizedRoute, keys, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around compile that handles path-to-regexp 6.3.0+ validation errors.\n * No server-side error reporting to avoid bundling issues.\n */\nexport function safeCompile(\n  route: string,\n  options?: TokensToFunctionOptions & ParseOptions\n) {\n  // Check if normalization is needed and cache the result\n  const needsNormalization = hasAdjacentParameterIssues(route)\n  const routeToUse = needsNormalization\n    ? normalizeAdjacentParameters(route)\n    : route\n\n  try {\n    return compile(routeToUse, options)\n  } catch (error) {\n    // Only try normalization if we haven't already normalized\n    if (!needsNormalization) {\n      try {\n        const normalizedRoute = normalizeAdjacentParameters(route)\n        return compile(normalizedRoute, options)\n      } catch (retryError) {\n        // If that doesn't work, fall back to original error\n        throw error\n      }\n    }\n    throw error\n  }\n}\n\n/**\n * Client-safe wrapper around regexpToFunction that automatically cleans parameters.\n */\nexport function safeRegexpToFunction<\n  T extends Record<string, any> = Record<string, any>,\n>(regexp: RegExp, keys?: Key[]): (pathname: string) => { params: T } | false {\n  const originalMatcher = regexpToFunction<T>(regexp, keys || [])\n\n  return (pathname: string) => {\n    const result = originalMatcher(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return {\n      ...result,\n      params: stripParameterSeparators(result.params as any) as T,\n    }\n  }\n}\n\n/**\n * Safe wrapper for route matcher functions that automatically cleans parameters.\n * This is client-safe and doesn't import path-to-regexp.\n */\nexport function safeRouteMatcher<T extends Record<string, any>>(\n  matcherFn: (pathname: string) => false | T\n): (pathname: string) => false | T {\n  return (pathname: string) => {\n    const result = matcherFn(pathname)\n    if (!result) return false\n\n    // Clean parameters before returning\n    return stripParameterSeparators(result) as T\n  }\n}\n"], "names": ["safeCompile", "safePathToRegexp", "safeRegexpToFunction", "safeRouteMatcher", "route", "keys", "options", "pathToRegexp", "needsNormalization", "hasAdjacentParameterIssues", "routeToUse", "normalizeAdjacentParameters", "error", "normalizedRoute", "retryError", "compile", "regexp", "originalMatcher", "regexpToFunction", "pathname", "result", "params", "stripParameterSeparators", "matcherFn"], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;IA2DeA,WAAW,EAAA;eAAXA;;IApCAC,gBAAgB,EAAA;eAAhBA;;IAkEAC,oBAAoB,EAAA;eAApBA;;IAqBAC,gBAAgB,EAAA;eAAhBA;;;8BAlGT;wCAKA;AAMA,SAASF,iBACdG,KAA+C,EAC/CC,IAAY,EACZC,OAA8C;IAE9C,IAAI,OAAOF,UAAU,UAAU;QAC7B,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACH,OAAOC,MAAMC;IACnC;IAEA,wDAAwD;IACxD,MAAME,qBAAqBC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACG,YAAYL,MAAMC;IACxC,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP;gBACpD,OAAOG,CAAAA,GAAAA,cAAAA,YAAY,EAACM,iBAAiBR,MAAMC;YAC7C,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAMO,SAASZ,YACdI,KAAa,EACbE,OAAgD;IAEhD,wDAAwD;IACxD,MAAME,qBAAqBC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACL;IACtD,MAAMM,aAAaF,qBACfG,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP,SAC5BA;IAEJ,IAAI;QACF,OAAOW,CAAAA,GAAAA,cAAAA,OAAO,EAACL,YAAYJ;IAC7B,EAAE,OAAOM,OAAO;QACd,0DAA0D;QAC1D,IAAI,CAACJ,oBAAoB;YACvB,IAAI;gBACF,MAAMK,kBAAkBF,CAAAA,GAAAA,wBAAAA,2BAA2B,EAACP;gBACpD,OAAOW,CAAAA,GAAAA,cAAAA,OAAO,EAACF,iBAAiBP;YAClC,EAAE,OAAOQ,YAAY;gBACnB,oDAAoD;gBACpD,MAAMF;YACR;QACF;QACA,MAAMA;IACR;AACF;AAKO,SAASV,qBAEdc,MAAc,EAAEX,IAAY;IAC5B,MAAMY,kBAAkBC,CAAAA,GAAAA,cAAAA,gBAAgB,EAAIF,QAAQX,QAAQ,EAAE;IAE9D,OAAO,CAACc;QACN,MAAMC,SAASH,gBAAgBE;QAC/B,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAO;YACL,GAAGA,MAAM;YACTC,QAAQC,CAAAA,GAAAA,wBAAAA,wBAAwB,EAACF,OAAOC,MAAM;QAChD;IACF;AACF;AAMO,SAASlB,iBACdoB,SAA0C;IAE1C,OAAO,CAACJ;QACN,MAAMC,SAASG,UAAUJ;QACzB,IAAI,CAACC,QAAQ,OAAO;QAEpB,oCAAoC;QACpC,OAAOE,CAAAA,GAAAA,wBAAAA,wBAAwB,EAACF;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\nimport { safeRouteMatcher } from './route-match-utils'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  const rawMatcher = (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n\n  // Wrap with safe matcher to handle parameter cleaning\n  return safeRouteMatcher(rawMatcher)\n}\n"], "names": ["getRouteMatcher", "re", "groups", "rawMatcher", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "DecodeError", "params", "key", "group", "Object", "entries", "match", "pos", "undefined", "repeat", "split", "map", "entry", "safeRouteMatcher"], "mappings": ";;;+BAgBgBA,mBAAAA;;;eAAAA;;;uBAfY;iCAEK;AAa1B,SAASA,gBAAgB,KAGV;IAHU,IAAA,EAC9BC,EAAE,EACFC,MAAM,EACc,GAHU;IAI9B,MAAMC,aAAa,CAACC;QAClB,MAAMC,aAAaJ,GAAGK,IAAI,CAACF;QAC3B,IAAI,CAACC,YAAY,OAAO;QAExB,MAAME,SAAS,CAACC;YACd,IAAI;gBACF,OAAOC,mBAAmBD;YAC5B,EAAE,OAAA,GAAM;gBACN,MAAM,OAAA,cAAyC,CAAzC,IAAIE,OAAAA,WAAW,CAAC,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,MAAMC,SAAiB,CAAC;QACxB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACb,QAAS;YACjD,MAAMc,QAAQX,UAAU,CAACQ,MAAMI,GAAG,CAAC;YACnC,IAAID,UAAUE,WAAW;gBACvB,IAAIL,MAAMM,MAAM,EAAE;oBAChBR,MAAM,CAACC,IAAI,GAAGI,MAAMI,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,QAAUf,OAAOe;gBACvD,OAAO;oBACLX,MAAM,CAACC,IAAI,GAAGL,OAAOS;gBACvB;YACF;QACF;QAEA,OAAOL;IACT;IAEA,sDAAsD;IACtD,OAAOY,CAAAA,GAAAA,iBAAAA,gBAAgB,EAACpB;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/parse-relative-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\nexport interface ParsedRelativeUrl {\n  hash: string\n  href: string\n  pathname: string\n  query: ParsedUrlQuery\n  search: string\n  slashes: undefined\n}\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery?: true\n): ParsedRelativeUrl\nexport function parseRelativeUrl(\n  url: string,\n  base: string | undefined,\n  parseQuery: false\n): Omit<ParsedRelativeUrl, 'query'>\nexport function parseRelativeUrl(\n  url: string,\n  base?: string,\n  parseQuery = true\n): ParsedRelativeUrl | Omit<ParsedRelativeUrl, 'query'> {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n\n  const resolvedBase = base\n    ? new URL(base, globalBase)\n    : url.startsWith('.')\n      ? new URL(\n          typeof window === 'undefined' ? 'http://n' : window.location.href\n        )\n      : globalBase\n\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n\n  return {\n    pathname,\n    query: parseQuery ? searchParamsToUrlQuery(searchParams) : undefined,\n    search,\n    hash,\n    href: href.slice(origin.length),\n    // We don't know for relative URLs at this point since we set a custom, internal\n    // base that isn't surfaced to users.\n    slashes: undefined,\n  }\n}\n"], "names": ["parseRelativeUrl", "url", "base", "parse<PERSON><PERSON>y", "globalBase", "URL", "window", "getLocationOrigin", "resolvedBase", "startsWith", "location", "href", "pathname", "searchParams", "search", "hash", "origin", "Error", "query", "searchParamsToUrlQuery", "undefined", "slice", "length", "slashes"], "mappings": ";;;+BA6BgBA,oBAAAA;;;eAAAA;;;uBA5BkB;6BACK;AA2BhC,SAASA,iBACdC,GAAW,EACXC,IAAa,EACbC,UAAiB;IAAjBA,IAAAA,eAAAA,KAAAA,GAAAA,aAAa;IAEb,MAAMC,aAAa,IAAIC,IACrB,OAAOC,WAAW,qBAAc,aAAaC,IAAAA,wBAAiB;IAGhE,MAAMC,eAAeN,OACjB,IAAIG,IAAIH,MAAME,cACdH,IAAIQ,UAAU,CAAC,OACb,IAAIJ,IACF,OAAOC,WAAW,qBAAc,aAAaA,OAAOI,QAAQ,CAACC,IAAI,OAEnEP;IAEN,MAAM,EAAEQ,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAEJ,IAAI,EAAEK,MAAM,EAAE,GAAG,IAAIX,IACjEJ,KACAO;IAGF,IAAIQ,WAAWZ,WAAWY,MAAM,EAAE;QAChC,MAAM,OAAA,cAAoE,CAApE,IAAIC,MAAO,sDAAmDhB,MAA9D,qBAAA;mBAAA;wBAAA;0BAAA;QAAmE;IAC3E;IAEA,OAAO;QACLW;QACAM,OAAOf,aAAagB,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,gBAAgBO;QAC3DN;QACAC;QACAJ,MAAMA,KAAKU,KAAK,CAACL,OAAOM,MAAM;QAC9B,gFAAgF;QAChF,qCAAqC;QACrCC,SAASH;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/parse-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\n\nexport interface ParsedUrl {\n  hash: string\n  hostname?: string | null\n  href: string\n  pathname: string\n  port?: string | null\n  protocol?: string | null\n  query: ParsedUrlQuery\n  search: string\n  slashes: boolean | undefined\n}\n\nexport function parseUrl(url: string): ParsedUrl {\n  if (url.startsWith('/')) {\n    return parseRelativeUrl(url)\n  }\n\n  const parsedURL = new URL(url)\n  return {\n    hash: parsedURL.hash,\n    hostname: parsedURL.hostname,\n    href: parsedURL.href,\n    pathname: parsedURL.pathname,\n    port: parsedURL.port,\n    protocol: parsedURL.protocol,\n    query: searchParamsToUrlQuery(parsedURL.searchParams),\n    search: parsedURL.search,\n    slashes:\n      parsedURL.href.slice(\n        parsedURL.protocol.length,\n        parsedURL.protocol.length + 2\n      ) === '//',\n  }\n}\n"], "names": ["parseUrl", "url", "startsWith", "parseRelativeUrl", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParamsToUrlQuery", "searchParams", "search", "slashes", "slice", "length"], "mappings": ";;;+BAiBgBA,YAAAA;;;eAAAA;;;6BAfuB;kCACN;AAc1B,SAASA,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACF;IAC1B;IAEA,MAAMG,YAAY,IAAIC,IAAIJ;IAC1B,OAAO;QACLK,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACT,UAAUU,YAAY;QACpDC,QAAQX,UAAUW,MAAM;QACxBC,SACEZ,UAAUI,IAAI,CAACS,KAAK,CAClBb,UAAUO,QAAQ,CAACO,MAAM,EACzBd,UAAUO,QAAQ,CAACO,MAAM,GAAG,OACxB;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/prepare-destination.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { Key } from 'next/dist/compiled/path-to-regexp'\nimport type { NextParsedUrlQuery } from '../../../../server/request-meta'\nimport type { RouteHas } from '../../../../lib/load-custom-routes'\nimport type { BaseNextRequest } from '../../../../server/base-http'\n\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { parseUrl } from './parse-url'\nimport {\n  INTERCEPTION_ROUTE_MARKERS,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\nimport { getCookieParser } from '../../../../server/api-utils/get-cookie-parser'\nimport type { Params } from '../../../../server/request/params'\nimport { safePathToRegexp, safeCompile } from './route-match-utils'\n\n/**\n * Ensure only a-zA-Z are used for param names for proper interpolating\n * with path-to-regexp\n */\nfunction getSafeParamName(paramName: string) {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nfunction escapeSegment(str: string, segmentName: string) {\n  return str.replace(\n    new RegExp(`:${escapeStringRegexp(segmentName)}`, 'g'),\n    `__ESC_COLON_${segmentName}`\n  )\n}\n\nfunction unescapeSegments(str: string) {\n  return str.replace(/__ESC_COLON_/gi, ':')\n}\n\nexport function matchHas(\n  req: BaseNextRequest | IncomingMessage,\n  query: Params,\n  has: RouteHas[] = [],\n  missing: RouteHas[] = []\n): false | Params {\n  const params: Params = {}\n\n  const hasMatch = (hasItem: RouteHas) => {\n    let value\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        if ('cookies' in req) {\n          value = req.cookies[hasItem.key]\n        } else {\n          const cookies = getCookieParser(req.headers)()\n          value = cookies[hasItem.key]\n        }\n\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':', 1)[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = Array.isArray(value)\n        ? value.slice(-1)[0].match(matcher)\n        : value.match(matcher)\n\n      if (matches) {\n        if (Array.isArray(matches)) {\n          if (matches.groups) {\n            Object.keys(matches.groups).forEach((groupKey) => {\n              params[groupKey] = matches.groups![groupKey]\n            })\n          } else if (hasItem.type === 'host' && matches[0]) {\n            params.host = matches[0]\n          }\n        }\n        return true\n      }\n    }\n    return false\n  }\n\n  const allMatch =\n    has.every((item) => hasMatch(item)) &&\n    !missing.some((item) => hasMatch(item))\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return safeCompile(`/${value}`, { validate: false })(params).slice(1)\n}\n\nexport function parseDestination(args: {\n  destination: string\n  params: Readonly<Params>\n  query: Readonly<NextParsedUrlQuery>\n}) {\n  let escaped = args.destination\n  for (const param of Object.keys({ ...args.params, ...args.query })) {\n    if (!param) continue\n\n    escaped = escapeSegment(escaped, param)\n  }\n\n  const parsed = parseUrl(escaped)\n\n  let pathname = parsed.pathname\n  if (pathname) {\n    pathname = unescapeSegments(pathname)\n  }\n\n  let href = parsed.href\n  if (href) {\n    href = unescapeSegments(href)\n  }\n\n  let hostname = parsed.hostname\n  if (hostname) {\n    hostname = unescapeSegments(hostname)\n  }\n\n  let hash = parsed.hash\n  if (hash) {\n    hash = unescapeSegments(hash)\n  }\n\n  let search = parsed.search\n  if (search) {\n    search = unescapeSegments(search)\n  }\n\n  return {\n    ...parsed,\n    pathname,\n    hostname,\n    href,\n    hash,\n    search,\n  }\n}\n\nexport function prepareDestination(args: {\n  appendParamsToQuery: boolean\n  destination: string\n  params: Params\n  query: NextParsedUrlQuery\n}) {\n  const parsedDestination = parseDestination(args)\n\n  const {\n    hostname: destHostname,\n    query: destQuery,\n    search: destSearch,\n  } = parsedDestination\n\n  // The following code assumes that the pathname here includes the hash if it's\n  // present.\n  let destPath = parsedDestination.pathname\n  if (parsedDestination.hash) {\n    destPath = `${destPath}${parsedDestination.hash}`\n  }\n\n  const destParams: (string | number)[] = []\n\n  const destPathParamKeys: Key[] = []\n  safePathToRegexp(destPath, destPathParamKeys)\n  for (const key of destPathParamKeys) {\n    destParams.push(key.name)\n  }\n\n  if (destHostname) {\n    const destHostnameParamKeys: Key[] = []\n    safePathToRegexp(destHostname, destHostnameParamKeys)\n    for (const key of destHostnameParamKeys) {\n      destParams.push(key.name)\n    }\n  }\n\n  const destPathCompiler = safeCompile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n\n  let destHostnameCompiler\n  if (destHostname) {\n    destHostnameCompiler = safeCompile(destHostname, { validate: false })\n  }\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    // the value needs to start with a forward-slash to be compiled\n    // correctly\n    if (Array.isArray(strOrArray)) {\n      destQuery[key] = strOrArray.map((value) =>\n        compileNonPath(unescapeSegments(value), args.params)\n      )\n    } else if (typeof strOrArray === 'string') {\n      destQuery[key] = compileNonPath(unescapeSegments(strOrArray), args.params)\n    }\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(args.params).filter(\n    (name) => name !== 'nextInternalLocale'\n  )\n\n  if (\n    args.appendParamsToQuery &&\n    !paramKeys.some((key) => destParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = args.params[key]\n      }\n    }\n  }\n\n  let newUrl\n\n  // The compiler also that the interception route marker is an unnamed param, hence '0',\n  // so we need to add it to the params object.\n  if (isInterceptionRouteAppPath(destPath)) {\n    for (const segment of destPath.split('/')) {\n      const marker = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n        segment.startsWith(m)\n      )\n      if (marker) {\n        if (marker === '(..)(..)') {\n          args.params['0'] = '(..)'\n          args.params['1'] = '(..)'\n        } else {\n          args.params['0'] = marker\n        }\n        break\n      }\n    }\n  }\n\n  try {\n    newUrl = destPathCompiler(args.params)\n\n    const [pathname, hash] = newUrl.split('#', 2)\n    if (destHostnameCompiler) {\n      parsedDestination.hostname = destHostnameCompiler(args.params)\n    }\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    parsedDestination.search = destSearch\n      ? compileNonPath(destSearch, args.params)\n      : ''\n  } catch (err: any) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...args.query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    destQuery,\n    parsedDestination,\n  }\n}\n"], "names": ["compileNonPath", "matchHas", "parseDestination", "prepareDestination", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "escapeStringRegexp", "unescapeSegments", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "includes", "safeCompile", "validate", "args", "escaped", "destination", "param", "parsed", "parseUrl", "pathname", "href", "hash", "search", "parsedDestination", "destHostname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destSearch", "destPath", "destParams", "destPathPara<PERSON><PERSON><PERSON>s", "safePathToRegexp", "push", "name", "destHostnameParamKeys", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "isInterceptionRouteAppPath", "segment", "marker", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "err", "message", "Error"], "mappings": ";;;;;;;;;;;;;;;;IA8HgBA,cAAc,EAAA;eAAdA;;IA/EAC,QAAQ,EAAA;eAARA;;IAkHAC,gBAAgB,EAAA;eAAhBA;;IAiDAC,kBAAkB,EAAA;eAAlBA;;;8BA5MmB;0BACV;oCAIlB;iCACyB;iCAEc;AAE9C;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACGE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAQ,MAAGC,CAAAA,GAAAA,cAAAA,kBAAkB,EAACH,cAAgB,MACjD,iBAAcA;AAEnB;AAEA,SAASI,iBAAiBL,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEO,SAASb,SACdiB,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,QAAAA,KAAAA,GAAAA,MAAkB,EAAE;IACpBC,IAAAA,YAAAA,KAAAA,GAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAUC,CAAAA,GAAAA,iBAAAA,eAAe,EAACb,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEM,IAAI,EAAE,GAAGd,CAAAA,OAAAA,OAAAA,KAAAA,IAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMI,WAAWD,QAAAA,OAAAA,KAAAA,IAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACN,WAAW;oBACnDH,QAAQQ;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACT,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMU,UAAU,IAAIpB,OAAQ,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMW,UAAUC,MAAMC,OAAO,CAACb,SAC1BA,MAAMc,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBV,MAAMe,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCvB,MAAM,CAACuB,SAAS,GAAGT,QAAQK,MAAO,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIrB,QAAQG,IAAI,KAAK,UAAUS,OAAO,CAAC,EAAE,EAAE;wBAChDd,OAAOU,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJ1B,IAAI2B,KAAK,CAAC,CAACC,OAASzB,SAASyB,UAC7B,CAAC3B,QAAQ4B,IAAI,CAAC,CAACD,OAASzB,SAASyB;IAEnC,IAAIF,UAAU;QACZ,OAAOxB;IACT;IACA,OAAO;AACT;AAEO,SAAStB,eAAeyB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOgB,OAAOC,IAAI,CAACrB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAE,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MACxB,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MACxB,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAQ,MAAGW,MAAI,OAAM,MAAO,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAQ,MAAGW,MAAI,WAAU,MAC5B,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOqC,CAAAA,GAAAA,iBAAAA,WAAW,EAAE,MAAG1B,OAAS;QAAE2B,UAAU;IAAM,GAAG9B,QAAQiB,KAAK,CAAC;AACrE;AAEO,SAASrC,iBAAiBmD,IAIhC;IACC,IAAIC,UAAUD,KAAKE,WAAW;IAC9B,KAAK,MAAMC,SAASd,OAAOC,IAAI,CAAC;QAAE,GAAGU,KAAK/B,MAAM;QAAE,GAAG+B,KAAKlC,KAAK;IAAC,GAAI;QAClE,IAAI,CAACqC,OAAO;QAEZF,UAAU3C,cAAc2C,SAASE;IACnC;IAEA,MAAMC,SAASC,CAAAA,GAAAA,UAAAA,QAAQ,EAACJ;IAExB,IAAIK,WAAWF,OAAOE,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAW1C,iBAAiB0C;IAC9B;IAEA,IAAIC,OAAOH,OAAOG,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO3C,iBAAiB2C;IAC1B;IAEA,IAAI3B,WAAWwB,OAAOxB,QAAQ;IAC9B,IAAIA,UAAU;QACZA,WAAWhB,iBAAiBgB;IAC9B;IAEA,IAAI4B,OAAOJ,OAAOI,IAAI;IACtB,IAAIA,MAAM;QACRA,OAAO5C,iBAAiB4C;IAC1B;IAEA,IAAIC,SAASL,OAAOK,MAAM;IAC1B,IAAIA,QAAQ;QACVA,SAAS7C,iBAAiB6C;IAC5B;IAEA,OAAO;QACL,GAAGL,MAAM;QACTE;QACA1B;QACA2B;QACAC;QACAC;IACF;AACF;AAEO,SAAS3D,mBAAmBkD,IAKlC;IACC,MAAMU,oBAAoB7D,iBAAiBmD;IAE3C,MAAM,EACJpB,UAAU+B,YAAY,EACtB7C,OAAO8C,SAAS,EAChBH,QAAQI,UAAU,EACnB,GAAGH;IAEJ,8EAA8E;IAC9E,WAAW;IACX,IAAII,WAAWJ,kBAAkBJ,QAAQ;IACzC,IAAII,kBAAkBF,IAAI,EAAE;QAC1BM,WAAY,KAAEA,WAAWJ,kBAAkBF,IAAI;IACjD;IAEA,MAAMO,aAAkC,EAAE;IAE1C,MAAMC,oBAA2B,EAAE;IACnCC,CAAAA,GAAAA,iBAAAA,gBAAgB,EAACH,UAAUE;IAC3B,KAAK,MAAM3C,OAAO2C,kBAAmB;QACnCD,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;IAC1B;IAEA,IAAIR,cAAc;QAChB,MAAMS,wBAA+B,EAAE;QACvCH,CAAAA,GAAAA,iBAAAA,gBAAgB,EAACN,cAAcS;QAC/B,KAAK,MAAM/C,OAAO+C,sBAAuB;YACvCL,WAAWG,IAAI,CAAC7C,IAAI8C,IAAI;QAC1B;IACF;IAEA,MAAME,mBAAmBvB,CAAAA,GAAAA,iBAAAA,WAAW,EAClCgB,UACA,AACA,oEADoE,AACA;IACpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEf,UAAU;IAAM;IAGpB,IAAIuB;IACJ,IAAIX,cAAc;QAChBW,uBAAuBxB,CAAAA,GAAAA,iBAAAA,WAAW,EAACa,cAAc;YAAEZ,UAAU;QAAM;IACrE;IAEA,oCAAoC;IACpC,KAAK,MAAM,CAAC1B,KAAKkD,WAAW,IAAIlC,OAAOmC,OAAO,CAACZ,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI5B,MAAMC,OAAO,CAACsC,aAAa;YAC7BX,SAAS,CAACvC,IAAI,GAAGkD,WAAWE,GAAG,CAAC,CAACrD,QAC/BzB,eAAeiB,iBAAiBQ,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOsD,eAAe,UAAU;YACzCX,SAAS,CAACvC,IAAI,GAAG1B,eAAeiB,iBAAiB2D,aAAavB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAIyD,YAAYrC,OAAOC,IAAI,CAACU,KAAK/B,MAAM,EAAE0D,MAAM,CAC7C,CAACR,OAASA,SAAS;IAGrB,IACEnB,KAAK4B,mBAAmB,IACxB,CAACF,UAAU9B,IAAI,CAAC,CAACvB,MAAQ0C,WAAWlB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOqD,UAAW;YAC3B,IAAI,CAAErD,CAAAA,OAAOuC,SAAQ,GAAI;gBACvBA,SAAS,CAACvC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAIwD;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIC,CAAAA,GAAAA,oBAAAA,0BAA0B,EAAChB,WAAW;QACxC,KAAK,MAAMiB,WAAWjB,SAASjC,KAAK,CAAC,KAAM;YACzC,MAAMmD,SAASC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IAC9CJ,QAAQK,UAAU,CAACD;YAErB,IAAIH,QAAQ;gBACV,IAAIA,WAAW,YAAY;oBACzBhC,KAAK/B,MAAM,CAAC,IAAI,GAAG;oBACnB+B,KAAK/B,MAAM,CAAC,IAAI,GAAG;gBACrB,OAAO;oBACL+B,KAAK/B,MAAM,CAAC,IAAI,GAAG+D;gBACrB;gBACA;YACF;QACF;IACF;IAEA,IAAI;QACFH,SAASR,iBAAiBrB,KAAK/B,MAAM;QAErC,MAAM,CAACqC,UAAUE,KAAK,GAAGqB,OAAOhD,KAAK,CAAC,KAAK;QAC3C,IAAIyC,sBAAsB;YACxBZ,kBAAkB9B,QAAQ,GAAG0C,qBAAqBtB,KAAK/B,MAAM;QAC/D;QACAyC,kBAAkBJ,QAAQ,GAAGA;QAC7BI,kBAAkBF,IAAI,GAAI,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvDE,kBAAkBD,MAAM,GAAGI,aACvBlE,eAAekE,YAAYb,KAAK/B,MAAM,IACtC;IACN,EAAE,OAAOoE,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAACnD,KAAK,CAAC,iDAAiD;YACrE,MAAM,OAAA,cAEL,CAFK,IAAIoD,MACP,4KADG,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxC3B,kBAAkB5C,KAAK,GAAG;QACxB,GAAGkC,KAAKlC,KAAK;QACb,GAAG4C,kBAAkB5C,KAAK;IAC5B;IAEA,OAAO;QACL+D;QACAjB;QACAF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1567, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;;;;;;;;;;;;;;;;IAQzBA,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IAAIH,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "hexHash", "str", "hash", "i", "length", "char", "charCodeAt", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;;;;;;;;;IACxCA,QAAQ,EAAA;eAARA;;IASAC,OAAO,EAAA;eAAPA;;;AATT,SAASD,SAASE,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASF,QAAQC,GAAW;IACjC,OAAOF,SAASE,KAAKM,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/is-app-route-route.ts"], "sourcesContent": ["export function isAppRouteRoute(route: string): boolean {\n  return route.endsWith('/route')\n}\n"], "names": ["isAppRouteRoute", "route", "endsWith"], "mappings": ";;;+BAAgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,KAAa;IAC3C,OAAOA,MAAMC,QAAQ,CAAC;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/metadata/is-metadata-route.ts"], "sourcesContent": ["import type { PageExtensions } from '../../build/page-extensions-type'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { isAppRouteRoute } from '../is-app-route-route'\n\nexport const STATIC_METADATA_IMAGES = {\n  icon: {\n    filename: 'icon',\n    extensions: ['ico', 'jpg', 'jpeg', 'png', 'svg'],\n  },\n  apple: {\n    filename: 'apple-icon',\n    extensions: ['jpg', 'jpeg', 'png'],\n  },\n  favicon: {\n    filename: 'favicon',\n    extensions: ['ico'],\n  },\n  openGraph: {\n    filename: 'opengraph-image',\n    extensions: ['jpg', 'jpeg', 'png', 'gif'],\n  },\n  twitter: {\n    filename: 'twitter-image',\n    extensions: ['jpg', 'jpeg', 'png', 'gif'],\n  },\n} as const\n\n// Match routes that are metadata routes, e.g. /sitemap.xml, /favicon.<ext>, /<icon>.<ext>, etc.\n// TODO-METADATA: support more metadata routes with more extensions\nexport const DEFAULT_METADATA_ROUTE_EXTENSIONS = ['js', 'jsx', 'ts', 'tsx']\n\n// Match the file extension with the dynamic multi-routes extensions\n// e.g. ([xml, js], null) -> can match `/sitemap.xml/route`, `sitemap.js/route`\n// e.g. ([png], [ts]) -> can match `/opengrapg-image.png`, `/opengraph-image.ts`\nexport const getExtensionRegexString = (\n  staticExtensions: readonly string[],\n  dynamicExtensions: readonly string[] | null\n) => {\n  // If there's no possible multi dynamic routes, will not match any <name>[].<ext> files\n  if (!dynamicExtensions || dynamicExtensions.length === 0) {\n    return `(\\\\.(?:${staticExtensions.join('|')}))`\n  }\n  return `(?:\\\\.(${staticExtensions.join('|')})|(\\\\.(${dynamicExtensions.join('|')})))`\n}\n\n/**\n * Determine if the file is a metadata route file entry\n * @param appDirRelativePath the relative file path to app/\n * @param pageExtensions the js extensions, such as ['js', 'jsx', 'ts', 'tsx']\n * @param strictlyMatchExtensions if it's true, match the file with page extension, otherwise match the file with default corresponding extension\n * @returns if the file is a metadata route file\n */\nexport function isMetadataRouteFile(\n  appDirRelativePath: string,\n  pageExtensions: PageExtensions,\n  strictlyMatchExtensions: boolean\n) {\n  // End with the extension or optional to have the extension\n  // When strictlyMatchExtensions is true, it's used for match file path;\n  // When strictlyMatchExtensions, the dynamic extension is skipped but\n  // static extension is kept, which is usually used for matching route path.\n  const trailingMatcher = (strictlyMatchExtensions ? '' : '?') + '$'\n  // Match the optional variants like /opengraph-image2, /icon-a102f4.png, etc.\n  const variantsMatcher = '\\\\d?'\n  // The -\\w{6} is the suffix that normalized from group routes;\n  const groupSuffix = strictlyMatchExtensions ? '' : '(-\\\\w{6})?'\n\n  const suffixMatcher = `${variantsMatcher}${groupSuffix}`\n\n  const metadataRouteFilesRegex = [\n    new RegExp(\n      `^[\\\\\\\\/]robots${getExtensionRegexString(\n        pageExtensions.concat('txt'),\n        null\n      )}${trailingMatcher}`\n    ),\n    new RegExp(\n      `^[\\\\\\\\/]manifest${getExtensionRegexString(\n        pageExtensions.concat('webmanifest', 'json'),\n        null\n      )}${trailingMatcher}`\n    ),\n    new RegExp(`^[\\\\\\\\/]favicon\\\\.ico$`),\n    new RegExp(\n      `[\\\\\\\\/]sitemap${getExtensionRegexString(['xml'], pageExtensions)}${trailingMatcher}`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.icon.filename}${suffixMatcher}${getExtensionRegexString(\n        STATIC_METADATA_IMAGES.icon.extensions,\n        pageExtensions\n      )}${trailingMatcher}`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.apple.filename}${suffixMatcher}${getExtensionRegexString(\n        STATIC_METADATA_IMAGES.apple.extensions,\n        pageExtensions\n      )}${trailingMatcher}`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.openGraph.filename}${suffixMatcher}${getExtensionRegexString(\n        STATIC_METADATA_IMAGES.openGraph.extensions,\n        pageExtensions\n      )}${trailingMatcher}`\n    ),\n    new RegExp(\n      `[\\\\\\\\/]${STATIC_METADATA_IMAGES.twitter.filename}${suffixMatcher}${getExtensionRegexString(\n        STATIC_METADATA_IMAGES.twitter.extensions,\n        pageExtensions\n      )}${trailingMatcher}`\n    ),\n  ]\n\n  const normalizedAppDirRelativePath = normalizePathSep(appDirRelativePath)\n  const matched = metadataRouteFilesRegex.some((r) =>\n    r.test(normalizedAppDirRelativePath)\n  )\n\n  return matched\n}\n\n// Check if the route is a static metadata route, with /route suffix\n// e.g. /favicon.ico/route, /icon.png/route, etc.\n// But skip the text routes like robots.txt since they might also be dynamic.\n// Checking route path is not enough to determine if text routes is dynamic.\nexport function isStaticMetadataRoute(route: string) {\n  // extract ext with regex\n  const pathname = route.replace(/\\/route$/, '')\n\n  const matched =\n    isAppRouteRoute(route) &&\n    isMetadataRouteFile(pathname, [], true) &&\n    // These routes can either be built by static or dynamic entrypoints,\n    // so we assume they're dynamic\n    pathname !== '/robots.txt' &&\n    pathname !== '/manifest.webmanifest' &&\n    !pathname.endsWith('/sitemap.xml')\n\n  return matched\n}\n\n/**\n * Determine if a page or pathname is a metadata page.\n *\n * The input is a page or pathname, which can be with or without page suffix /foo/page or /foo.\n * But it will not contain the /route suffix.\n *\n * .e.g\n * /robots -> true\n * /sitemap -> true\n * /foo -> false\n */\nexport function isMetadataPage(page: string) {\n  const matched = !isAppRouteRoute(page) && isMetadataRouteFile(page, [], false)\n\n  return matched\n}\n\n/*\n * Determine if a Next.js route is a metadata route.\n * `route` will has a route suffix.\n *\n * e.g.\n * /app/robots/route -> true\n * /robots/route -> true\n * /sitemap/[__metadata_id__]/route -> true\n * /app/sitemap/page -> false\n * /icon-a102f4/route -> true\n */\nexport function isMetadataRoute(route: string): boolean {\n  let page = normalizeAppPath(route)\n    .replace(/^\\/?app\\//, '')\n    // Remove the dynamic route id\n    .replace('/[__metadata_id__]', '')\n    // Remove the /route suffix\n    .replace(/\\/route$/, '')\n\n  if (page[0] !== '/') page = '/' + page\n\n  const matched = isAppRouteRoute(route) && isMetadataRouteFile(page, [], false)\n\n  return matched\n}\n"], "names": ["DEFAULT_METADATA_ROUTE_EXTENSIONS", "STATIC_METADATA_IMAGES", "getExtensionRegexString", "isMetadataPage", "isMetadataRoute", "isMetadataRouteFile", "isStaticMetadataRoute", "icon", "filename", "extensions", "apple", "favicon", "openGraph", "twitter", "staticExtensions", "dynamicExtensions", "length", "join", "appDirRelativePath", "pageExtensions", "strictlyMatchExtensions", "trailing<PERSON><PERSON><PERSON>", "variantsMatcher", "groupSuffix", "suffixMatcher", "metadataRouteFilesRegex", "RegExp", "concat", "normalizedAppDirRelativePath", "normalizePathSep", "matched", "some", "r", "test", "route", "pathname", "replace", "isAppRouteRoute", "endsWith", "page", "normalizeAppPath"], "mappings": ";;;;;;;;;;;;;;;;;;;IA8BaA,iCAAiC,EAAA;eAAjCA;;IAzBAC,sBAAsB,EAAA;eAAtBA;;IA8BAC,uBAAuB,EAAA;eAAvBA;;IAqHGC,cAAc,EAAA;eAAdA;;IAiBAC,eAAe,EAAA;eAAfA;;IApHAC,mBAAmB,EAAA;eAAnBA;;IAwEAC,qBAAqB,EAAA;eAArBA;;;kCA5HiB;0BACA;iCACD;AAEzB,MAAML,yBAAyB;IACpCM,MAAM;QACJC,UAAU;QACVC,YAAY;YAAC;YAAO;YAAO;YAAQ;YAAO;SAAM;IAClD;IACAC,OAAO;QACLF,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;SAAM;IACpC;IACAE,SAAS;QACPH,UAAU;QACVC,YAAY;YAAC;SAAM;IACrB;IACAG,WAAW;QACTJ,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;IACAI,SAAS;QACPL,UAAU;QACVC,YAAY;YAAC;YAAO;YAAQ;YAAO;SAAM;IAC3C;AACF;AAIO,MAAMT,oCAAoC;IAAC;IAAM;IAAO;IAAM;CAAM;AAKpE,MAAME,0BAA0B,CACrCY,kBACAC;IAEA,uFAAuF;IACvF,IAAI,CAACA,qBAAqBA,kBAAkBC,MAAM,KAAK,GAAG;QACxD,OAAO,CAAC,OAAO,EAAEF,iBAAiBG,IAAI,CAAC,KAAK,EAAE,CAAC;IACjD;IACA,OAAO,CAAC,OAAO,EAAEH,iBAAiBG,IAAI,CAAC,KAAK,OAAO,EAAEF,kBAAkBE,IAAI,CAAC,KAAK,GAAG,CAAC;AACvF;AASO,SAASZ,oBACda,kBAA0B,EAC1BC,cAA8B,EAC9BC,uBAAgC;IAEhC,2DAA2D;IAC3D,uEAAuE;IACvE,qEAAqE;IACrE,2EAA2E;IAC3E,MAAMC,kBAAmBD,CAAAA,0BAA0B,KAAK,GAAE,IAAK;IAC/D,6EAA6E;IAC7E,MAAME,kBAAkB;IACxB,8DAA8D;IAC9D,MAAMC,cAAcH,0BAA0B,KAAK;IAEnD,MAAMI,gBAAgB,GAAGF,kBAAkBC,aAAa;IAExD,MAAME,0BAA0B;QAC9B,IAAIC,OACF,CAAC,cAAc,EAAExB,wBACfiB,eAAeQ,MAAM,CAAC,QACtB,QACEN,iBAAiB;QAEvB,IAAIK,OACF,CAAC,gBAAgB,EAAExB,wBACjBiB,eAAeQ,MAAM,CAAC,eAAe,SACrC,QACEN,iBAAiB;QAEvB,IAAIK,OAAO,CAAC,sBAAsB,CAAC;QACnC,IAAIA,OACF,CAAC,cAAc,EAAExB,wBAAwB;YAAC;SAAM,EAAEiB,kBAAkBE,iBAAiB;QAEvF,IAAIK,OACF,CAAC,OAAO,EAAEzB,uBAAuBM,IAAI,CAACC,QAAQ,GAAGgB,gBAAgBtB,wBAC/DD,uBAAuBM,IAAI,CAACE,UAAU,EACtCU,kBACEE,iBAAiB;QAEvB,IAAIK,OACF,CAAC,OAAO,EAAEzB,uBAAuBS,KAAK,CAACF,QAAQ,GAAGgB,gBAAgBtB,wBAChED,uBAAuBS,KAAK,CAACD,UAAU,EACvCU,kBACEE,iBAAiB;QAEvB,IAAIK,OACF,CAAC,OAAO,EAAEzB,uBAAuBW,SAAS,CAACJ,QAAQ,GAAGgB,gBAAgBtB,wBACpED,uBAAuBW,SAAS,CAACH,UAAU,EAC3CU,kBACEE,iBAAiB;QAEvB,IAAIK,OACF,CAAC,OAAO,EAAEzB,uBAAuBY,OAAO,CAACL,QAAQ,GAAGgB,gBAAgBtB,wBAClED,uBAAuBY,OAAO,CAACJ,UAAU,EACzCU,kBACEE,iBAAiB;KAExB;IAED,MAAMO,+BAA+BC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACX;IACtD,MAAMY,UAAUL,wBAAwBM,IAAI,CAAC,CAACC,IAC5CA,EAAEC,IAAI,CAACL;IAGT,OAAOE;AACT;AAMO,SAASxB,sBAAsB4B,KAAa;IACjD,yBAAyB;IACzB,MAAMC,WAAWD,MAAME,OAAO,CAAC,YAAY;IAE3C,MAAMN,UACJO,CAAAA,GAAAA,iBAAAA,eAAe,EAACH,UAChB7B,oBAAoB8B,UAAU,EAAE,EAAE,SAClC,qEAAqE;IACrE,+BAA+B;IAC/BA,aAAa,iBACbA,aAAa,2BACb,CAACA,SAASG,QAAQ,CAAC;IAErB,OAAOR;AACT;AAaO,SAAS3B,eAAeoC,IAAY;IACzC,MAAMT,UAAU,CAACO,CAAAA,GAAAA,iBAAAA,eAAe,EAACE,SAASlC,oBAAoBkC,MAAM,EAAE,EAAE;IAExE,OAAOT;AACT;AAaO,SAAS1B,gBAAgB8B,KAAa;IAC3C,IAAIK,OAAOC,CAAAA,GAAAA,UAAAA,gBAAgB,EAACN,OACzBE,OAAO,CAAC,aAAa,IACtB,8BAA8B;KAC7BA,OAAO,CAAC,sBAAsB,IAC/B,2BAA2B;KAC1BA,OAAO,CAAC,YAAY;IAEvB,IAAIG,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAElC,MAAMT,UAAUO,CAAAA,GAAAA,iBAAAA,eAAe,EAACH,UAAU7B,oBAAoBkC,MAAM,EAAE,EAAE;IAExE,OAAOT;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/constants.ts"], "sourcesContent": ["import type { ServerRuntime } from '../types'\n\nexport const TEXT_PLAIN_CONTENT_TYPE_HEADER = 'text/plain'\nexport const HTML_CONTENT_TYPE_HEADER = 'text/html; charset=utf-8'\nexport const JSON_CONTENT_TYPE_HEADER = 'application/json; charset=utf-8'\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS =\n  'private-next-rsc-track-dynamic-import'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n"], "names": ["ACTION_SUFFIX", "APP_DIR_ALIAS", "CACHE_ONE_YEAR", "DOT_NEXT_ALIAS", "ESLINT_DEFAULT_DIRS", "GSP_NO_RETURNED_VALUE", "GSSP_COMPONENT_MEMBER_ERROR", "GSSP_NO_RETURNED_VALUE", "HTML_CONTENT_TYPE_HEADER", "INFINITE_CACHE", "INSTRUMENTATION_HOOK_FILENAME", "JSON_CONTENT_TYPE_HEADER", "MATCHED_PATH_HEADER", "MIDDLEWARE_FILENAME", "MIDDLEWARE_LOCATION_REGEXP", "NEXT_BODY_SUFFIX", "NEXT_CACHE_IMPLICIT_TAG_ID", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "NEXT_CACHE_SOFT_TAG_MAX_LENGTH", "NEXT_CACHE_TAGS_HEADER", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "NEXT_DATA_SUFFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_META_SUFFIX", "NEXT_QUERY_PARAM_PREFIX", "NEXT_RESUME_HEADER", "NON_STANDARD_NODE_ENV", "PAGES_DIR_ALIAS", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "ROOT_DIR_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_VALIDATE_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "RSC_DYNAMIC_IMPORT_WRAPPER_ALIAS", "RSC_MOD_REF_PROXY_ALIAS", "RSC_PREFETCH_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "RSC_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "SERVER_PROPS_GET_INIT_PROPS_CONFLICT", "SERVER_PROPS_SSG_CONFLICT", "SERVER_RUNTIME", "SSG_FALLBACK_EXPORT_ERROR", "SSG_GET_INITIAL_PROPS_CONFLICT", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "TEXT_PLAIN_CONTENT_TYPE_HEADER", "UNSTABLE_REVALIDATE_RENAME_ERROR", "WEBPACK_LAYERS", "WEBPACK_RESOURCE_QUERIES", "edge", "experimentalEdge", "nodejs", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "edgeSSREntry", "metadata", "metadataRoute", "metadataImageMeta"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiBaA,aAAa,EAAA;eAAbA;;IAuCAC,aAAa,EAAA;eAAbA;;IAnBAC,cAAc,EAAA;eAAdA;;IAiBAC,cAAc,EAAA;eAAdA;;IAwCAC,mBAAmB,EAAA;eAAnBA;;IAfAC,qBAAqB,EAAA;eAArBA;;IASAC,2BAA2B,EAAA;eAA3BA;;IAPAC,sBAAsB,EAAA;eAAtBA;;IA9EAC,wBAAwB,EAAA;eAAxBA;;IAuCAC,cAAc,EAAA;eAAdA;;IAOAC,6BAA6B,EAAA;eAA7BA;;IA7CAC,wBAAwB,EAAA;eAAxBA;;IAIAC,mBAAmB,EAAA;eAAnBA;;IAqCAC,mBAAmB,EAAA;eAAnBA;;IACAC,0BAA0B,EAAA;eAA1BA;;IA1BAC,gBAAgB,EAAA;eAAhBA;;IAcAC,0BAA0B,EAAA;eAA1BA;;IAXAC,kCAAkC,EAAA;eAAlCA;;IACAC,sCAAsC,EAAA;eAAtCA;;IASAC,8BAA8B,EAAA;eAA9BA;;IAXAC,sBAAsB,EAAA;eAAtBA;;IASAC,wBAAwB,EAAA;eAAxBA;;IACAC,yBAAyB,EAAA;eAAzBA;;IAdAC,gBAAgB,EAAA;eAAhBA;;IAZAC,+BAA+B,EAAA;eAA/BA;;IAaAC,gBAAgB,EAAA;eAAhBA;;IAdAC,uBAAuB,EAAA;eAAvBA;;IAsBAC,kBAAkB,EAAA;eAAlBA;;IA+DAC,qBAAqB,EAAA;eAArBA;;IArCAC,eAAe,EAAA;eAAfA;;IA5CAC,2BAA2B,EAAA;eAA3BA;;IACAC,0CAA0C,EAAA;eAA1CA;;IAyDAC,8BAA8B,EAAA;eAA9BA;;IAZAC,cAAc,EAAA;eAAdA;;IASAC,+BAA+B,EAAA;eAA/BA;;IADAC,2BAA2B,EAAA;eAA3BA;;IAJAC,sBAAsB,EAAA;eAAtBA;;IADAC,yBAAyB,EAAA;eAAzBA;;IAEAC,uBAAuB,EAAA;eAAvBA;;IACAC,gCAAgC,EAAA;eAAhCA;;IAJAC,uBAAuB,EAAA;eAAvBA;;IA5CAC,mBAAmB,EAAA;eAAnBA;;IACAC,uBAAuB,EAAA;eAAvBA;;IACAC,kBAAkB,EAAA;eAAlBA;;IACAC,UAAU,EAAA;eAAVA;;IA6DAC,yBAAyB,EAAA;eAAzBA;;IANAC,oCAAoC,EAAA;eAApCA;;IAEAC,yBAAyB,EAAA;eAAzBA;;IAuBAC,cAAc,EAAA;eAAdA;;IAJAC,yBAAyB,EAAA;eAAzBA;;IAvBAC,8BAA8B,EAAA;eAA9BA;;IAMAC,0CAA0C,EAAA;eAA1CA;;IAzEAC,8BAA8B,EAAA;eAA9BA;;IAkFAC,gCAAgC,EAAA;eAAhCA;;IAiIJC,cAAc,EAAA;eAAdA;;IAAgBC,wBAAwB,EAAA;eAAxBA;;;AAnNlB,MAAMH,iCAAiC;AACvC,MAAM5C,2BAA2B;AACjC,MAAMG,2BAA2B;AACjC,MAAMe,0BAA0B;AAChC,MAAMF,kCAAkC;AAExC,MAAMZ,sBAAsB;AAC5B,MAAMkB,8BAA8B;AACpC,MAAMC,6CACX;AAEK,MAAMU,sBAAsB;AAC5B,MAAMC,0BAA0B;AAChC,MAAMC,qBAAqB;AAC3B,MAAMC,aAAa;AACnB,MAAM5C,gBAAgB;AACtB,MAAMuB,mBAAmB;AACzB,MAAME,mBAAmB;AACzB,MAAMV,mBAAmB;AAEzB,MAAMK,yBAAyB;AAC/B,MAAMH,qCAAqC;AAC3C,MAAMC,yCACX;AAEK,MAAMS,qBAAqB;AAI3B,MAAMN,2BAA2B;AACjC,MAAMC,4BAA4B;AAClC,MAAMH,iCAAiC;AACvC,MAAMH,6BAA6B;AAGnC,MAAMd,iBAAiB;AAKvB,MAAMO,iBAAiB;AAGvB,MAAMI,sBAAsB;AAC5B,MAAMC,6BAA6B,CAAC,SAAS,EAAED,qBAAqB;AAGpE,MAAMH,gCAAgC;AAItC,MAAMmB,kBAAkB;AACxB,MAAM1B,iBAAiB;AACvB,MAAM8B,iBAAiB;AACvB,MAAMhC,gBAAgB;AACtB,MAAMuC,0BAA0B;AAChC,MAAMH,4BAA4B;AAClC,MAAMD,yBAAyB;AAC/B,MAAME,0BAA0B;AAChC,MAAMC,mCACX;AACK,MAAMJ,8BAA8B;AACpC,MAAMD,kCACX;AAEK,MAAMF,iCAAiC,CAAC,6KAA6K,CAAC;AAEtN,MAAMkB,iCAAiC,CAAC,mGAAmG,CAAC;AAE5I,MAAMJ,uCAAuC,CAAC,uFAAuF,CAAC;AAEtI,MAAMC,4BAA4B,CAAC,sHAAsH,CAAC;AAE1J,MAAMI,6CAA6C,CAAC,uGAAuG,CAAC;AAE5J,MAAMN,4BAA4B,CAAC,uHAAuH,CAAC;AAE3J,MAAMxC,wBACX;AACK,MAAME,yBACX;AAEK,MAAM8C,mCACX,uEACA;AAEK,MAAM/C,8BAA8B,CAAC,wJAAwJ,CAAC;AAE9L,MAAMsB,wBAAwB,CAAC,iNAAiN,CAAC;AAEjP,MAAMqB,4BAA4B,CAAC,wJAAwJ,CAAC;AAE5L,MAAM7C,sBAAsB;IAAC;IAAO;IAAS;IAAc;IAAO;CAAM;AAExE,MAAM4C,iBAAgD;IAC3DQ,MAAM;IACNC,kBAAkB;IAClBC,QAAQ;AACV;AAEA;;;CAGC,GACD,MAAMC,uBAAuB;IAC3B;;GAEC,GACDC,QAAQ;IACR;;;GAGC,GACDC,uBAAuB;IACvB;;GAEC,GACDC,qBAAqB;IACrB;;GAEC,GACDC,eAAe;IACf;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,SAAS;IACT;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,YAAY;IACZ;;GAEC,GACDC,WAAW;IACX;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,iBAAiB;IACjB;;GAEC,GACDC,cAAc;IACd;;GAEC,GACDC,cAAc;AAChB;AAKA,MAAMlB,iBAAiB;IACrB,GAAGK,oBAAoB;IACvBc,OAAO;QACLC,cAAc;YACZf,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;SACnC;QACDY,YAAY;YACVhB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDU,eAAe;YACb,YAAY;YACZjB,qBAAqBK,OAAO;YAC5BL,qBAAqBM,OAAO;SAC7B;QACDY,YAAY;YACVlB,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;SACrC;QACDS,SAAS;YACPnB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBI,aAAa;YAClCJ,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBC,MAAM;YAC3BD,qBAAqBQ,UAAU;YAC/BR,qBAAqBO,UAAU;SAChC;QACDa,UAAU;YACR,+BAA+B;YAC/BpB,qBAAqBE,qBAAqB;YAC1CF,qBAAqBG,mBAAmB;YACxCH,qBAAqBU,eAAe;YACpCV,qBAAqBI,aAAa;SACnC;IACH;AACF;AAEA,MAAMR,2BAA2B;IAC/ByB,cAAc;IACdC,UAAU;IACVC,eAAe;IACfC,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/route-pattern-normalizer.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\n\n/**\n * Route pattern normalization utilities for path-to-regexp compatibility.\n *\n * path-to-regexp 6.3.0+ introduced stricter validation that rejects certain\n * patterns commonly used in Next.js interception routes. This module provides\n * normalization functions to make Next.js route patterns compatible with the\n * updated library while preserving all functionality.\n */\n\n/**\n * Internal separator used to normalize adjacent parameter patterns.\n * This unique marker is inserted between adjacent parameters and stripped out\n * during parameter extraction to avoid conflicts with real URL content.\n */\nconst PARAM_SEPARATOR = '_NEXTSEP_'\n\n/**\n * Detects if a route pattern needs normalization for path-to-regexp compatibility.\n */\nexport function hasAdjacentParameterIssues(route: string): boolean {\n  if (typeof route !== 'string') return false\n\n  // Check for interception route markers followed immediately by parameters\n  // Pattern: /(.):param, /(..):param, /(...):param, /(.)(.):param etc.\n  // These patterns cause \"Must have text between two parameters\" errors\n  if (/\\/\\(\\.{1,3}\\):[^/\\s]+/.test(route)) {\n    return true\n  }\n\n  // Check for basic adjacent parameters without separators\n  // Pattern: :param1:param2 (but not :param* or other URL patterns)\n  if (/:[a-zA-Z_][a-zA-Z0-9_]*:[a-zA-Z_][a-zA-Z0-9_]*/.test(route)) {\n    return true\n  }\n\n  return false\n}\n\n/**\n * Normalizes route patterns that have adjacent parameters without text between them.\n * Inserts a unique separator that can be safely stripped out later.\n */\nexport function normalizeAdjacentParameters(route: string): string {\n  let normalized = route\n\n  // Handle interception route patterns: (.):param -> (.)_NEXTSEP_:param\n  normalized = normalized.replace(\n    /(\\([^)]*\\)):([^/\\s]+)/g,\n    `$1${PARAM_SEPARATOR}:$2`\n  )\n\n  // Handle other adjacent parameter patterns: :param1:param2 -> :param1_NEXTSEP_:param2\n  normalized = normalized.replace(/:([^:/\\s)]+)(?=:)/g, `:$1${PARAM_SEPARATOR}`)\n\n  return normalized\n}\n\n/**\n * Normalizes tokens that have repeating modifiers (* or +) but empty prefix and suffix.\n *\n * path-to-regexp 6.3.0+ introduced validation that throws:\n * \"Can not repeat without prefix/suffix\"\n *\n * This occurs when a token has modifier: '*' or '+' with both prefix: '' and suffix: ''\n */\nexport function normalizeTokensForRegexp(tokens: Token[]): Token[] {\n  return tokens.map((token) => {\n    // Token union type: Token = string | TokenObject\n    // Literal path segments are strings, parameters/wildcards are objects\n    if (\n      typeof token === 'object' &&\n      token !== null &&\n      // Not all token objects have 'modifier' property (e.g., simple text tokens)\n      'modifier' in token &&\n      // Only repeating modifiers (* or +) cause the validation error\n      // Other modifiers like '?' (optional) are fine\n      (token.modifier === '*' || token.modifier === '+') &&\n      // Token objects can have different shapes depending on route pattern\n      'prefix' in token &&\n      'suffix' in token &&\n      // Both prefix and suffix must be empty strings\n      // This is what causes the validation error in path-to-regexp\n      token.prefix === '' &&\n      token.suffix === ''\n    ) {\n      // Add minimal prefix to satisfy path-to-regexp validation\n      // We use '/' as it's the most common path delimiter and won't break route matching\n      // The prefix gets used in regex generation but doesn't affect parameter extraction\n      return {\n        ...token,\n        prefix: '/',\n      }\n    }\n    return token\n  })\n}\n\n/**\n * Strips normalization separators from extracted route parameters.\n * Used by both server and client code to clean up parameters after route matching.\n */\nexport function stripParameterSeparators(\n  params: Record<string, any>\n): Record<string, any> {\n  const cleaned: Record<string, any> = {}\n\n  for (const [key, value] of Object.entries(params)) {\n    if (typeof value === 'string') {\n      // Remove the separator if it appears at the start of parameter values\n      cleaned[key] = value.replace(new RegExp(`^${PARAM_SEPARATOR}`), '')\n    } else if (Array.isArray(value)) {\n      // Handle array parameters (from repeated route segments)\n      cleaned[key] = value.map((item) =>\n        typeof item === 'string'\n          ? item.replace(new RegExp(`^${PARAM_SEPARATOR}`), '')\n          : item\n      )\n    } else {\n      cleaned[key] = value\n    }\n  }\n\n  return cleaned\n}\n"], "names": ["hasAdjacentParameterIssues", "normalizeAdjacentParameters", "normalizeTokensForRegexp", "stripParameterSeparators", "PARAM_SEPARATOR", "route", "test", "normalized", "replace", "tokens", "map", "token", "modifier", "prefix", "suffix", "params", "cleaned", "key", "value", "Object", "entries", "RegExp", "Array", "isArray", "item"], "mappings": ";;;;;;;;;;;;;;;;IAqBgBA,0BAA0B,EAAA;eAA1BA;;IAuBAC,2BAA2B,EAAA;eAA3BA;;IAuBAC,wBAAwB,EAAA;eAAxBA;;IAoCAC,wBAAwB,EAAA;eAAxBA;;;AArGhB;;;;;;;CAOC,GAED;;;;CAIC,GACD,MAAMC,kBAAkB;AAKjB,SAASJ,2BAA2BK,KAAa;IACtD,IAAI,OAAOA,UAAU,UAAU,OAAO;IAEtC,0EAA0E;IAC1E,qEAAqE;IACrE,sEAAsE;IACtE,IAAI,wBAAwBC,IAAI,CAACD,QAAQ;QACvC,OAAO;IACT;IAEA,yDAAyD;IACzD,kEAAkE;IAClE,IAAI,iDAAiDC,IAAI,CAACD,QAAQ;QAChE,OAAO;IACT;IAEA,OAAO;AACT;AAMO,SAASJ,4BAA4BI,KAAa;IACvD,IAAIE,aAAaF;IAEjB,sEAAsE;IACtEE,aAAaA,WAAWC,OAAO,CAC7B,0BACA,CAAC,EAAE,EAAEJ,gBAAgB,GAAG,CAAC;IAG3B,sFAAsF;IACtFG,aAAaA,WAAWC,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAEJ,iBAAiB;IAE7E,OAAOG;AACT;AAUO,SAASL,yBAAyBO,MAAe;IACtD,OAAOA,OAAOC,GAAG,CAAC,CAACC;QACjB,iDAAiD;QACjD,sEAAsE;QACtE,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,4EAA4E;QAC5E,cAAcA,SACd,+DAA+D;QAC/D,+CAA+C;QAC9CA,CAAAA,MAAMC,QAAQ,KAAK,OAAOD,MAAMC,QAAQ,KAAK,GAAE,KAChD,qEAAqE;QACrE,YAAYD,SACZ,YAAYA,SACZ,+CAA+C;QAC/C,6DAA6D;QAC7DA,MAAME,MAAM,KAAK,MACjBF,MAAMG,MAAM,KAAK,IACjB;YACA,0DAA0D;YAC1D,mFAAmF;YACnF,mFAAmF;YACnF,OAAO;gBACL,GAAGH,KAAK;gBACRE,QAAQ;YACV;QACF;QACA,OAAOF;IACT;AACF;AAMO,SAASR,yBACdY,MAA2B;IAE3B,MAAMC,UAA+B,CAAC;IAEtC,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,QAAS;QACjD,IAAI,OAAOG,UAAU,UAAU;YAC7B,sEAAsE;YACtEF,OAAO,CAACC,IAAI,GAAGC,MAAMV,OAAO,CAAC,IAAIa,OAAO,CAAC,CAAC,EAAEjB,iBAAiB,GAAG;QAClE,OAAO,IAAIkB,MAAMC,OAAO,CAACL,QAAQ;YAC/B,yDAAyD;YACzDF,OAAO,CAACC,IAAI,GAAGC,MAAMR,GAAG,CAAC,CAACc,OACxB,OAAOA,SAAS,WACZA,KAAKhB,OAAO,CAAC,IAAIa,OAAO,CAAC,CAAC,EAAEjB,iBAAiB,GAAG,MAChDoB;QAER,OAAO;YACLR,OAAO,CAACC,IAAI,GAAGC;QACjB;IACF;IAEA,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/url.ts"], "sourcesContent": ["import type { UrlWithParsedQuery } from 'url'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst DUMMY_ORIGIN = 'http://n'\n\nexport function isFullStringUrl(url: string) {\n  return /https?:\\/\\//.test(url)\n}\n\nexport function parseUrl(url: string): URL | undefined {\n  let parsed: URL | undefined = undefined\n  try {\n    parsed = new URL(url, DUMMY_ORIGIN)\n  } catch {}\n  return parsed\n}\n\nexport function parseReqUrl(url: string): UrlWithParsedQuery | undefined {\n  const parsedUrl: URL | undefined = parseUrl(url)\n\n  if (!parsedUrl) {\n    return\n  }\n\n  const query: Record<string, string | string[]> = {}\n\n  for (const key of parsedUrl.searchParams.keys()) {\n    const values = parsedUrl.searchParams.getAll(key)\n    query[key] = values.length > 1 ? values : values[0]\n  }\n\n  const legacyUrl: UrlWithParsedQuery = {\n    query,\n    hash: parsedUrl.hash,\n    search: parsedUrl.search,\n    path: parsedUrl.pathname,\n    pathname: parsedUrl.pathname,\n    href: `${parsedUrl.pathname}${parsedUrl.search}${parsedUrl.hash}`,\n    host: '',\n    hostname: '',\n    auth: '',\n    protocol: '',\n    slashes: null,\n    port: '',\n  }\n  return legacyUrl\n}\n\nexport function stripNextRscUnionQuery(relativeUrl: string): string {\n  const urlInstance = new URL(relativeUrl, DUMMY_ORIGIN)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return urlInstance.pathname + urlInstance.search\n}\n"], "names": ["isFullStringUrl", "parseReqUrl", "parseUrl", "stripNextRscUnionQuery", "DUMMY_ORIGIN", "url", "test", "parsed", "undefined", "URL", "parsedUrl", "query", "key", "searchParams", "keys", "values", "getAll", "length", "legacyUrl", "hash", "search", "path", "pathname", "href", "host", "hostname", "auth", "protocol", "slashes", "port", "relativeUrl", "urlInstance", "delete", "NEXT_RSC_UNION_QUERY"], "mappings": ";;;;;;;;;;;;;;;;IAKgBA,eAAe,EAAA;eAAfA;;IAYAC,WAAW,EAAA;eAAXA;;IARAC,QAAQ,EAAA;eAARA;;IAuCAC,sBAAsB,EAAA;eAAtBA;;;kCA/CqB;AAErC,MAAMC,eAAe;AAEd,SAASJ,gBAAgBK,GAAW;IACzC,OAAO,cAAcC,IAAI,CAACD;AAC5B;AAEO,SAASH,SAASG,GAAW;IAClC,IAAIE,SAA0BC;IAC9B,IAAI;QACFD,SAAS,IAAIE,IAAIJ,KAAKD;IACxB,EAAE,OAAM,CAAC;IACT,OAAOG;AACT;AAEO,SAASN,YAAYI,GAAW;IACrC,MAAMK,YAA6BR,SAASG;IAE5C,IAAI,CAACK,WAAW;QACd;IACF;IAEA,MAAMC,QAA2C,CAAC;IAElD,KAAK,MAAMC,OAAOF,UAAUG,YAAY,CAACC,IAAI,GAAI;QAC/C,MAAMC,SAASL,UAAUG,YAAY,CAACG,MAAM,CAACJ;QAC7CD,KAAK,CAACC,IAAI,GAAGG,OAAOE,MAAM,GAAG,IAAIF,SAASA,MAAM,CAAC,EAAE;IACrD;IAEA,MAAMG,YAAgC;QACpCP;QACAQ,MAAMT,UAAUS,IAAI;QACpBC,QAAQV,UAAUU,MAAM;QACxBC,MAAMX,UAAUY,QAAQ;QACxBA,UAAUZ,UAAUY,QAAQ;QAC5BC,MAAM,GAAGb,UAAUY,QAAQ,GAAGZ,UAAUU,MAAM,GAAGV,UAAUS,IAAI,EAAE;QACjEK,MAAM;QACNC,UAAU;QACVC,MAAM;QACNC,UAAU;QACVC,SAAS;QACTC,MAAM;IACR;IACA,OAAOX;AACT;AAEO,SAASf,uBAAuB2B,WAAmB;IACxD,MAAMC,cAAc,IAAItB,IAAIqB,aAAa1B;IACzC2B,YAAYlB,YAAY,CAACmB,MAAM,CAACC,kBAAAA,oBAAoB;IAEpD,OAAOF,YAAYT,QAAQ,GAAGS,YAAYX,MAAM;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/generate-interception-routes-rewrites.ts"], "sourcesContent": ["import { NEXT_URL } from '../client/components/app-router-headers'\nimport {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../shared/lib/router/utils/interception-routes'\nimport type { Rewrite } from './load-custom-routes'\nimport { safePathToRegexp } from '../shared/lib/router/utils/route-match-utils'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\n\n// a function that converts normalised paths (e.g. /foo/[bar]/[baz]) to the format expected by pathToRegexp (e.g. /foo/:bar/:baz)\nfunction toPathToRegexpPath(path: string): string {\n  return path.replace(/\\[\\[?([^\\]]+)\\]\\]?/g, (_, capture) => {\n    // path-to-regexp only supports word characters, so we replace any non-word characters with underscores\n    const paramName = capture.replace(/\\W+/g, '_')\n\n    // handle catch-all segments (e.g. /foo/bar/[...baz] or /foo/bar/[[...baz]])\n    if (capture.startsWith('...')) {\n      return `:${capture.slice(3)}*`\n    }\n    return ':' + paramName\n  })\n}\n\nexport function generateInterceptionRoutesRewrites(\n  appPaths: string[],\n  basePath = ''\n): Rewrite[] {\n  const rewrites: Rewrite[] = []\n\n  for (const appPath of appPaths) {\n    if (isInterceptionRouteAppPath(appPath)) {\n      const { interceptingRoute, interceptedRoute } =\n        extractInterceptionRouteInformation(appPath)\n\n      const normalizedInterceptingRoute = `${\n        interceptingRoute !== '/' ? toPathToRegexpPath(interceptingRoute) : ''\n      }/(.*)?`\n\n      const normalizedInterceptedRoute = toPathToRegexpPath(interceptedRoute)\n      const normalizedAppPath = toPathToRegexpPath(appPath)\n\n      // pathToRegexp returns a regex that matches the path, but we need to\n      // convert it to a string that can be used in a header value\n      // to the format that Next/the proxy expects\n      let interceptingRouteRegex = safePathToRegexp(normalizedInterceptingRoute)\n        .toString()\n        .slice(2, -3)\n\n      rewrites.push({\n        source: `${basePath}${normalizedInterceptedRoute}`,\n        destination: `${basePath}${normalizedAppPath}`,\n        has: [\n          {\n            type: 'header',\n            key: NEXT_URL,\n            value: interceptingRouteRegex,\n          },\n        ],\n      })\n    }\n  }\n\n  return rewrites\n}\n\nexport function isInterceptionRouteRewrite(route: DeepReadonly<Rewrite>) {\n  // When we generate interception rewrites in the above implementation, we always do so with only a single `has` condition.\n  return route.has?.[0]?.key === NEXT_URL\n}\n"], "names": ["generateInterceptionRoutesRewrites", "isInterceptionRouteRewrite", "toPathToRegexpPath", "path", "replace", "_", "capture", "paramName", "startsWith", "slice", "appPaths", "basePath", "rewrites", "appPath", "isInterceptionRouteAppPath", "interceptingRoute", "interceptedRoute", "extractInterceptionRouteInformation", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "safePathToRegexp", "toString", "push", "source", "destination", "has", "type", "key", "NEXT_URL", "value", "route"], "mappings": ";;;;;;;;;;;;;;IAuBgBA,kCAAkC,EAAA;eAAlCA;;IA0CAC,0BAA0B,EAAA;eAA1BA;;;kCAjES;oCAIlB;iCAE0B;AAGjC,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,uGAAuG;QACvG,MAAMC,YAAYD,QAAQF,OAAO,CAAC,QAAQ;QAE1C,4EAA4E;QAC5E,IAAIE,QAAQE,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAEF,QAAQG,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEO,SAASP,mCACdU,QAAkB,EAClBC,WAAW,EAAE;IAEb,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWH,SAAU;QAC9B,IAAII,CAAAA,GAAAA,oBAAAA,0BAA0B,EAACD,UAAU;YACvC,MAAM,EAAEE,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CC,CAAAA,GAAAA,oBAAAA,mCAAmC,EAACJ;YAEtC,MAAMK,8BAA8B,GAClCH,sBAAsB,MAAMb,mBAAmBa,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMI,6BAA6BjB,mBAAmBc;YACtD,MAAMI,oBAAoBlB,mBAAmBW;YAE7C,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIQ,yBAAyBC,CAAAA,GAAAA,iBAAAA,gBAAgB,EAACJ,6BAC3CK,QAAQ,GACRd,KAAK,CAAC,GAAG,CAAC;YAEbG,SAASY,IAAI,CAAC;gBACZC,QAAQ,GAAGd,WAAWQ,4BAA4B;gBAClDO,aAAa,GAAGf,WAAWS,mBAAmB;gBAC9CO,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKC,kBAAAA,QAAQ;wBACbC,OAAOV;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOT;AACT;AAEO,SAASX,2BAA2B+B,KAA4B;QAE9DA,aAAAA;IADP,0HAA0H;IAC1H,OAAOA,CAAAA,CAAAA,aAAAA,MAAML,GAAG,KAAA,OAAA,KAAA,IAAA,CAATK,cAAAA,UAAW,CAAC,EAAE,KAAA,OAAA,KAAA,IAAdA,YAAgBH,GAAG,MAAKC,kBAAAA,QAAQ;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/metadata/get-metadata-route.ts"], "sourcesContent": ["import { isMetadataPage } from './is-metadata-route'\nimport path from '../../shared/lib/isomorphic/path'\nimport { interpolateDynamicPath } from '../../server/server-utils'\nimport { getNamedRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport { djb2Hash } from '../../shared/lib/hash'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { normalizePathSep } from '../../shared/lib/page-path/normalize-path-sep'\nimport {\n  isGroupSegment,\n  isParallelRouteSegment,\n} from '../../shared/lib/segment'\n\n/*\n * If there's special convention like (...) or @ in the page path,\n * Give it a unique hash suffix to avoid conflicts\n *\n * e.g.\n * /opengraph-image -> /opengraph-image\n * /(post)/opengraph-image.tsx -> /opengraph-image-[0-9a-z]{6}\n *\n * Sitemap is an exception, it should not have a suffix.\n * Each sitemap contains all the urls of sub routes, we don't have the case of duplicates `/(group)/sitemap.[ext]` and `/sitemap.[ext]` since they should be the same.\n * Hence we always normalize the urls for sitemap and do not append hash suffix, and ensure user-land only contains one sitemap per pathname.\n *\n * /sitemap -> /sitemap\n * /(post)/sitemap -> /sitemap\n */\nfunction getMetadataRouteSuffix(page: string) {\n  // Remove the last segment and get the parent pathname\n  // e.g. /parent/a/b/c -> /parent/a/b\n  // e.g. /parent/opengraph-image -> /parent\n  const parentPathname = path.dirname(page)\n  // Only apply suffix to metadata routes except for sitemaps\n  if (page.endsWith('/sitemap')) {\n    return ''\n  }\n\n  // Calculate the hash suffix based on the parent path\n  let suffix = ''\n  // Check if there's any special characters in the parent pathname.\n  const segments = parentPathname.split('/')\n  if (\n    segments.some((seg) => isGroupSegment(seg) || isParallelRouteSegment(seg))\n  ) {\n    // Hash the parent path to get a unique suffix\n    suffix = djb2Hash(parentPathname).toString(36).slice(0, 6)\n  }\n  return suffix\n}\n\n/**\n * Fill the dynamic segment in the metadata route\n *\n * Example:\n * fillMetadataSegment('/a/[slug]', { params: { slug: 'b' } }, 'open-graph') -> '/a/b/open-graph'\n *\n */\nexport function fillMetadataSegment(\n  segment: string,\n  params: any,\n  lastSegment: string\n) {\n  const pathname = normalizeAppPath(segment)\n  const routeRegex = getNamedRouteRegex(pathname, {\n    prefixRouteKeys: false,\n  })\n  const route = interpolateDynamicPath(pathname, params, routeRegex)\n  const { name, ext } = path.parse(lastSegment)\n  const pagePath = path.posix.join(segment, name)\n  const suffix = getMetadataRouteSuffix(pagePath)\n  const routeSuffix = suffix ? `-${suffix}` : ''\n\n  return normalizePathSep(path.join(route, `${name}${routeSuffix}${ext}`))\n}\n\n/**\n * Map metadata page key to the corresponding route\n *\n * static file page key:    /app/robots.txt -> /robots.xml -> /robots.txt/route\n * dynamic route page key:  /app/robots.tsx -> /robots -> /robots.txt/route\n *\n * @param page\n * @returns\n */\nexport function normalizeMetadataRoute(page: string) {\n  if (!isMetadataPage(page)) {\n    return page\n  }\n  let route = page\n  let suffix = ''\n  if (page === '/robots') {\n    route += '.txt'\n  } else if (page === '/manifest') {\n    route += '.webmanifest'\n  } else {\n    suffix = getMetadataRouteSuffix(page)\n  }\n  // Support both /<metadata-route.ext> and custom routes /<metadata-route>/route.ts.\n  // If it's a metadata file route, we need to append /[id]/route to the page.\n  if (!route.endsWith('/route')) {\n    const { dir, name: baseName, ext } = path.parse(route)\n    route = path.posix.join(\n      dir,\n      `${baseName}${suffix ? `-${suffix}` : ''}${ext}`,\n      'route'\n    )\n  }\n\n  return route\n}\n\n// Normalize metadata route page to either a single route or a dynamic route.\n// e.g. Input: /sitemap/route\n// when isDynamic is false, single route -> /sitemap.xml/route\n// when isDynamic is false, dynamic route -> /sitemap/[__metadata_id__]/route\n// also works for pathname such as /sitemap -> /sitemap.xml, but will not append /route suffix\nexport function normalizeMetadataPageToRoute(page: string, isDynamic: boolean) {\n  const isRoute = page.endsWith('/route')\n  const routePagePath = isRoute ? page.slice(0, -'/route'.length) : page\n  const metadataRouteExtension = routePagePath.endsWith('/sitemap')\n    ? '.xml'\n    : ''\n  const mapped = isDynamic\n    ? `${routePagePath}/[__metadata_id__]`\n    : `${routePagePath}${metadataRouteExtension}`\n\n  return mapped + (isRoute ? '/route' : '')\n}\n"], "names": ["fillMetadataSegment", "normalizeMetadataPageToRoute", "normalizeMetadataRoute", "getMetadataRouteSuffix", "page", "parentPathname", "path", "dirname", "endsWith", "suffix", "segments", "split", "some", "seg", "isGroupSegment", "isParallelRouteSegment", "djb2Hash", "toString", "slice", "segment", "params", "lastSegment", "pathname", "normalizeAppPath", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "route", "interpolateDynamicPath", "name", "ext", "parse", "pagePath", "posix", "join", "routeSuffix", "normalizePathSep", "isMetadataPage", "dir", "baseName", "isDynamic", "isRoute", "routePagePath", "length", "metadataRouteExtension", "mapped"], "mappings": ";;;;;;;;;;;;;;;IAyDgBA,mBAAmB,EAAA;eAAnBA;;IA2DAC,4BAA4B,EAAA;eAA5BA;;IAhCAC,sBAAsB,EAAA;eAAtBA;;;iCApFe;6DACd;6BACsB;4BACJ;sBACV;0BACQ;kCACA;yBAI1B;;;;;;AAEP;;;;;;;;;;;;;;CAcC,GACD,SAASC,uBAAuBC,IAAY;IAC1C,sDAAsD;IACtD,oCAAoC;IACpC,0CAA0C;IAC1C,MAAMC,iBAAiBC,MAAAA,OAAI,CAACC,OAAO,CAACH;IACpC,2DAA2D;IAC3D,IAAIA,KAAKI,QAAQ,CAAC,aAAa;QAC7B,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAIC,SAAS;IACb,kEAAkE;IAClE,MAAMC,WAAWL,eAAeM,KAAK,CAAC;IACtC,IACED,SAASE,IAAI,CAAC,CAACC,MAAQC,CAAAA,GAAAA,SAAAA,cAAc,EAACD,QAAQE,CAAAA,GAAAA,SAAAA,sBAAsB,EAACF,OACrE;QACA,8CAA8C;QAC9CJ,SAASO,CAAAA,GAAAA,MAAAA,QAAQ,EAACX,gBAAgBY,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;IAC1D;IACA,OAAOT;AACT;AASO,SAAST,oBACdmB,OAAe,EACfC,MAAW,EACXC,WAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ;IAClC,MAAMK,aAAaC,CAAAA,GAAAA,YAAAA,kBAAkB,EAACH,UAAU;QAC9CI,iBAAiB;IACnB;IACA,MAAMC,QAAQC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,UAAUF,QAAQI;IACvD,MAAM,EAAEK,IAAI,EAAEC,GAAG,EAAE,GAAGxB,MAAAA,OAAI,CAACyB,KAAK,CAACV;IACjC,MAAMW,WAAW1B,MAAAA,OAAI,CAAC2B,KAAK,CAACC,IAAI,CAACf,SAASU;IAC1C,MAAMpB,SAASN,uBAAuB6B;IACtC,MAAMG,cAAc1B,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG;IAE5C,OAAO2B,CAAAA,GAAAA,kBAAAA,gBAAgB,EAAC9B,MAAAA,OAAI,CAAC4B,IAAI,CAACP,OAAO,GAAGE,OAAOM,cAAcL,KAAK;AACxE;AAWO,SAAS5B,uBAAuBE,IAAY;IACjD,IAAI,CAACiC,CAAAA,GAAAA,iBAAAA,cAAc,EAACjC,OAAO;QACzB,OAAOA;IACT;IACA,IAAIuB,QAAQvB;IACZ,IAAIK,SAAS;IACb,IAAIL,SAAS,WAAW;QACtBuB,SAAS;IACX,OAAO,IAAIvB,SAAS,aAAa;QAC/BuB,SAAS;IACX,OAAO;QACLlB,SAASN,uBAAuBC;IAClC;IACA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAI,CAACuB,MAAMnB,QAAQ,CAAC,WAAW;QAC7B,MAAM,EAAE8B,GAAG,EAAET,MAAMU,QAAQ,EAAET,GAAG,EAAE,GAAGxB,MAAAA,OAAI,CAACyB,KAAK,CAACJ;QAChDA,QAAQrB,MAAAA,OAAI,CAAC2B,KAAK,CAACC,IAAI,CACrBI,KACA,GAAGC,WAAW9B,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKqB,KAAK,EAChD;IAEJ;IAEA,OAAOH;AACT;AAOO,SAAS1B,6BAA6BG,IAAY,EAAEoC,SAAkB;IAC3E,MAAMC,UAAUrC,KAAKI,QAAQ,CAAC;IAC9B,MAAMkC,gBAAgBD,UAAUrC,KAAKc,KAAK,CAAC,GAAG,CAAC,SAASyB,MAAM,IAAIvC;IAClE,MAAMwC,yBAAyBF,cAAclC,QAAQ,CAAC,cAClD,SACA;IACJ,MAAMqC,SAASL,YACX,GAAGE,cAAc,kBAAkB,CAAC,GACpC,GAAGA,gBAAgBE,wBAAwB;IAE/C,OAAOC,SAAUJ,CAAAA,UAAU,WAAW,EAAC;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/api-utils/get-cookie-parser.ts"], "sourcesContent": ["import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } =\n      require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "parse<PERSON><PERSON><PERSON>", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join"], "mappings": ";;;+BAOgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAE/B;IACC,OAAO,SAASC;QACd,MAAM,EAAEC,MAAM,EAAE,GAAGF;QAEnB,IAAI,CAACE,QAAQ;YACX,OAAO,CAAC;QACV;QAEA,MAAM,EAAEC,OAAOC,aAAa,EAAE,GAC5BC,QAAQ;QACV,OAAOD,cAAcE,MAAMC,OAAO,CAACL,UAAUA,OAAOM,IAAI,CAAC,QAAQN;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/web/utils.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n"], "names": ["fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "splitCookiesString", "toNodeOutgoingHttpHeaders", "validateURL", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "cookies", "toLowerCase", "url", "String", "URL", "error", "Error", "cause", "prefixes", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "prefix", "startsWith"], "mappings": ";;;;;;;;;;;;;;;;;IAegBA,2BAA2B,EAAA;eAA3BA;;IA8IAC,uBAAuB,EAAA;eAAvBA;;IAlHAC,kBAAkB,EAAA;eAAlBA;;IAyEAC,yBAAyB,EAAA;eAAzBA;;IAwBAC,WAAW,EAAA;eAAXA;;;2BAxIT;AAWA,SAASJ,4BACdK,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAYO,SAASJ,mBAAmBgB,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AASO,SAAShB,0BACdG,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM4B,UAAoB,EAAE;IAC5B,IAAI3B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI0B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQF,IAAI,IAAI7B,mBAAmBO;gBACnCJ,WAAW,CAACG,IAAI,GAAGyB,QAAQN,MAAM,KAAK,IAAIM,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL5B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAKO,SAASD,YAAY+B,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,OAAA,cAKL,CALK,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM,IAJX,qBAAA;mBAAA;wBAAA;0BAAA;QAKN;IACF;AACF;AAMO,SAASrC,wBAAwBO,GAAW;IACjD,MAAMiC,WAAW;QAACC,WAAAA,uBAAuB;QAAEC,WAAAA,+BAA+B;KAAC;IAC3E,KAAK,MAAMC,UAAUH,SAAU;QAC7B,IAAIjC,QAAQoC,UAAUpC,IAAIqC,UAAU,CAACD,SAAS;YAC5C,OAAOpC,IAAIwB,SAAS,CAACY,OAAOjB,MAAM;QACpC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/lib/decode-query-path-parameter.ts"], "sourcesContent": ["/**\n * Decodes a query path parameter.\n *\n * @param value - The value to decode.\n * @returns The decoded value.\n */\nexport function decodeQueryPathParameter(value: string) {\n  // When deployed to Vercel, the value may be encoded, so this attempts to\n  // decode it and returns the original value if it fails.\n  try {\n    return decodeURIComponent(value)\n  } catch {\n    return value\n  }\n}\n"], "names": ["decodeQueryPathParameter", "value", "decodeURIComponent"], "mappings": "AAAA;;;;;CAKC;;;+BACeA,4BAAAA;;;eAAAA;;;AAAT,SAASA,yBAAyBC,KAAa;IACpD,yEAAyE;IACzE,wDAAwD;IACxD,IAAI;QACF,OAAOC,mBAAmBD;IAC5B,EAAE,OAAM;QACN,OAAOA;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/app-render/types.ts"], "sourcesContent": ["import type { LoadComponentsReturnType } from '../load-components'\nimport type { ServerRuntime, SizeLimit } from '../../types'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n} from '../../server/config-shared'\nimport type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  HeadData,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport s from 'next/dist/compiled/superstruct'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { InstrumentationOnRequestError } from '../instrumentation/types'\nimport type { NextRequestHint } from '../web/adapter'\nimport type { BaseNextRequest } from '../base-http'\nimport type { IncomingMessage } from 'http'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\n\nexport type DynamicParamTypes =\n  | 'catchall'\n  | 'catchall-intercepted'\n  | 'optional-catchall'\n  | 'dynamic'\n  | 'dynamic-intercepted'\n\nconst dynamicParamTypesSchema = s.enums(['c', 'ci', 'oc', 'd', 'di'])\n\nexport type DynamicParamTypesShort = s.Infer<typeof dynamicParamTypesSchema>\n\nconst segmentSchema = s.union([\n  s.string(),\n\n  s.tuple([\n    // Param name\n    s.string(),\n    // Param cache key (almost the same as the value, but arrays are\n    // concatenated into strings)\n    // TODO: We should change this to just be the value. Currently we convert\n    // it back to a value when passing to useParams. It only needs to be\n    // a string when converted to a a cache key, but that doesn't mean we\n    // need to store it as that representation.\n    s.string(),\n    // Dynamic param type\n    dynamicParamTypesSchema,\n  ]),\n])\n\nexport type Segment = s.Infer<typeof segmentSchema>\n\n// unfortunately the tuple is not understood well by Describe so we have to\n// use any here. This does not have any impact on the runtime type since the validation\n// does work correctly.\nexport const flightRouterStateSchema: s.Describe<any> = s.tuple([\n  segmentSchema,\n  s.record(\n    s.string(),\n    s.lazy(() => flightRouterStateSchema)\n  ),\n  s.optional(s.nullable(s.string())),\n  s.optional(\n    s.nullable(\n      s.union([\n        s.literal('refetch'),\n        s.literal('refresh'),\n        s.literal('inside-shared-layout'),\n        s.literal('metadata-only'),\n      ])\n    )\n  ),\n  s.optional(s.boolean()),\n])\n\n/**\n * Router state\n */\nexport type FlightRouterState = [\n  segment: Segment,\n  parallelRoutes: { [parallelRouterKey: string]: FlightRouterState },\n  url?: string | null,\n  /**\n   * \"refresh\" and \"refetch\", despite being similarly named, have different\n   * semantics:\n   * - \"refetch\" is used during a request to inform the server where rendering\n   *   should start from.\n   *\n   * - \"refresh\" is used by the client to mark that a segment should re-fetch the\n   *   data from the server for the current segment. It uses the \"url\" property\n   *   above to determine where to fetch from.\n   *\n   * - \"inside-shared-layout\" is used during a prefetch request to inform the\n   *   server that even if the segment matches, it should be treated as if it's\n   *   within the \"new\" part of a navigation — inside the shared layout. If\n   *   the segment doesn't match, then it has no effect, since it would be\n   *   treated as new regardless. If it does match, though, the server does not\n   *   need to render it, because the client already has it.\n   *\n   * - \"metadata-only\" instructs the server to skip rendering the segments and\n   *   only send the head data.\n   *\n   *   A bit confusing, but that's because it has only one extremely narrow use\n   *   case — during a non-PPR prefetch, the server uses it to find the first\n   *   loading boundary beneath a shared layout.\n   *\n   *   TODO: We should rethink the protocol for dynamic requests. It might not\n   *   make sense for the client to send a FlightRouterState, since this type is\n   *   overloaded with concerns.\n   */\n  refresh?:\n    | 'refetch'\n    | 'refresh'\n    | 'inside-shared-layout'\n    | 'metadata-only'\n    | null,\n  isRootLayout?: boolean,\n  /**\n   * Only present when responding to a tree prefetch request. Indicates whether\n   * there is a loading boundary somewhere in the tree. The client cache uses\n   * this to determine if it can skip the data prefetch request.\n   */\n  hasLoadingBoundary?: HasLoadingBoundary,\n]\n\nexport const enum HasLoadingBoundary {\n  // There is a loading boundary in this particular segment\n  SegmentHasLoadingBoundary = 1,\n  // There is a loading boundary somewhere in the subtree (but not in\n  // this segment)\n  SubtreeHasLoadingBoundary = 2,\n  // There is no loading boundary in this segment or any of its descendants\n  SubtreeHasNoLoadingBoundary = 3,\n}\n\n/**\n * Individual Flight response path\n */\nexport type FlightSegmentPath =\n  // Uses `any` as repeating pattern can't be typed.\n  | any[]\n  // Looks somewhat like this\n  | [\n      segment: Segment,\n      parallelRouterKey: string,\n      segment: Segment,\n      parallelRouterKey: string,\n      segment: Segment,\n      parallelRouterKey: string,\n    ]\n\n/**\n * Represents a tree of segments and the Flight data (i.e. React nodes) that\n * correspond to each one. The tree is isomorphic to the FlightRouterState;\n * however in the future we want to be able to fetch arbitrary partial segments\n * without having to fetch all its children. So this response format will\n * likely change.\n */\nexport type CacheNodeSeedData = [\n  segment: Segment,\n  node: React.ReactNode | null,\n  parallelRoutes: {\n    [parallelRouterKey: string]: CacheNodeSeedData | null\n  },\n  loading: LoadingModuleData | Promise<LoadingModuleData>,\n  isPartial: boolean,\n]\n\nexport type FlightDataSegment = [\n  /* segment of the rendered slice: */ Segment,\n  /* treePatch */ FlightRouterState,\n  /* cacheNodeSeedData */ CacheNodeSeedData | null, // Can be null during prefetch if there's no loading component\n  /* head: viewport */ HeadData,\n  /* isHeadPartial */ boolean,\n]\n\nexport type FlightDataPath =\n  // Uses `any` as repeating pattern can't be typed.\n  | any[]\n  // Looks somewhat like this\n  | [\n      // Holds full path to the segment.\n      ...FlightSegmentPath[],\n      ...FlightDataSegment,\n    ]\n\n/**\n * The Flight response data\n */\nexport type FlightData = Array<FlightDataPath> | string\n\nexport type ActionResult = Promise<any>\n\nexport type ServerOnInstrumentationRequestError = (\n  error: unknown,\n  // The request could be middleware, node server or web server request,\n  // we normalized them into an aligned format to `onRequestError` API later.\n  request: NextRequestHint | BaseNextRequest | IncomingMessage,\n  errorContext: Parameters<InstrumentationOnRequestError>[2]\n) => void | Promise<void>\n\nexport interface RenderOptsPartial {\n  dir?: string\n  previewProps: __ApiPreviewProps | undefined\n  err?: Error | null\n  dev?: boolean\n  basePath: string\n  trailingSlash: boolean\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  supportsDynamicResponse: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  enableTainting?: boolean\n  assetPrefix?: string\n  crossOrigin?: '' | 'anonymous' | 'use-credentials' | undefined\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  incrementalCache?: import('../lib/incremental-cache').IncrementalCache\n  cacheLifeProfiles?: {\n    [profile: string]: import('../use-cache/cache-life').CacheLife\n  }\n  isOnDemandRevalidate?: boolean\n  isPossibleServerAction?: boolean\n  setIsrStatus?: (key: string, value: boolean | null) => void\n  isRevalidate?: boolean\n  nextExport?: boolean\n  nextConfigOutput?: 'standalone' | 'export'\n  onInstrumentationRequestError?: ServerOnInstrumentationRequestError\n  isDraftMode?: boolean\n  deploymentId?: string\n  onUpdateCookies?: (cookies: string[]) => void\n  loadConfig?: (\n    phase: string,\n    dir: string,\n    customConfig?: object | null,\n    rawConfig?: boolean,\n    silent?: boolean\n  ) => Promise<NextConfigComplete>\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  params?: ParsedUrlQuery\n  isPrefetch?: boolean\n  htmlLimitedBots: string | undefined\n  experimental: {\n    /**\n     * When true, it indicates that the current page supports partial\n     * prerendering.\n     */\n    isRoutePPREnabled?: boolean\n    expireTime: number | undefined\n    staleTimes: ExperimentalConfig['staleTimes'] | undefined\n    clientTraceMetadata: string[] | undefined\n    cacheComponents: boolean\n    clientSegmentCache: boolean | 'client-only'\n    clientParamParsing: boolean\n    dynamicOnHover: boolean\n    inlineCss: boolean\n    authInterrupts: boolean\n  }\n  postponed?: string\n\n  /**\n   * Should wait for react stream allReady to resolve all suspense boundaries,\n   * in order to perform a full page render.\n   */\n  shouldWaitOnAllReady?: boolean\n\n  /**\n   * A prefilled resume data cache. This was either generated for this page\n   * during dev warmup, or when a page with defined params was previously\n   * prerendered, and now its matching optional fallback shell is prerendered.\n   */\n  renderResumeDataCache?: RenderResumeDataCache\n\n  /**\n   * When true, the page will be rendered using the static rendering to detect\n   * any dynamic API's that would have stopped the page from being fully\n   * statically generated.\n   */\n  isDebugDynamicAccesses?: boolean\n\n  /**\n   * This is true when:\n   * - source maps are generated\n   * - source maps are applied\n   * - minification is disabled\n   */\n  hasReadableErrorStacks?: boolean\n\n  /**\n   * The maximum length of the headers that are emitted by React and added to\n   * the response.\n   */\n  reactMaxHeadersLength: number | undefined\n\n  isStaticGeneration?: boolean\n\n  /**\n   * When true, the page is prerendered as a fallback shell, while allowing any\n   * dynamic accesses to result in an empty shell. This is the case when there\n   * are also routes prerendered with a more complete set of params.\n   * Prerendering those routes would catch any invalid dynamic accesses.\n   */\n  allowEmptyStaticShell?: boolean\n\n  /**\n   * next config experimental.devtoolSegmentExplorer\n   */\n  devtoolSegmentExplorer?: boolean\n}\n\nexport type RenderOpts = LoadComponentsReturnType<AppPageModule> &\n  RenderOptsPartial &\n  RequestLifecycleOpts\n\nexport type PreloadCallbacks = (() => void)[]\n\nexport type InitialRSCPayload = {\n  /** buildId */\n  b: string\n  /** assetPrefix */\n  p: string\n  /** initialCanonicalUrlParts */\n  c: string[]\n  /** couldBeIntercepted */\n  i: boolean\n  /** initialFlightData */\n  f: FlightDataPath[]\n  /** missingSlots */\n  m: Set<string> | undefined\n  /** GlobalError */\n  G: [React.ComponentType<any>, React.ReactNode | undefined]\n  /** postponed */\n  s: boolean\n  /** prerendered */\n  S: boolean\n}\n\n// Response from `createFromFetch` for normal rendering\nexport type NavigationFlightResponse = {\n  /** buildId */\n  b: string\n  /** flightData */\n  f: FlightData\n  /** prerendered */\n  S: boolean\n}\n\n// Response from `createFromFetch` for server actions. Action's flight data can be null\nexport type ActionFlightResponse = {\n  /** actionResult */\n  a: ActionResult\n  /** buildId */\n  b: string\n  /** flightData */\n  f: FlightData\n}\n\nexport type RSCPayload =\n  | InitialRSCPayload\n  | NavigationFlightResponse\n  | ActionFlightResponse\n"], "names": ["HasLoadingBoundary", "flightRouterStateSchema", "dynamicParamTypesSchema", "s", "enums", "segmentSchema", "union", "string", "tuple", "record", "lazy", "optional", "nullable", "literal", "boolean"], "mappings": ";;;;;;;;;;;;;;IAiIkBA,kBAAkB,EAAA;eAAlBA;;IAtELC,uBAAuB,EAAA;eAAvBA;;;oEA1CC;;;;;;AAed,MAAMC,0BAA0BC,aAAAA,OAAC,CAACC,KAAK,CAAC;IAAC;IAAK;IAAM;IAAM;IAAK;CAAK;AAIpE,MAAMC,gBAAgBF,aAAAA,OAAC,CAACG,KAAK,CAAC;IAC5BH,aAAAA,OAAC,CAACI,MAAM;IAERJ,aAAAA,OAAC,CAACK,KAAK,CAAC;QACN,aAAa;QACbL,aAAAA,OAAC,CAACI,MAAM;QACR,gEAAgE;QAChE,6BAA6B;QAC7B,yEAAyE;QACzE,oEAAoE;QACpE,qEAAqE;QACrE,2CAA2C;QAC3CJ,aAAAA,OAAC,CAACI,MAAM;QACR,qBAAqB;QACrBL;KACD;CACF;AAOM,MAAMD,0BAA2CE,aAAAA,OAAC,CAACK,KAAK,CAAC;IAC9DH;IACAF,aAAAA,OAAC,CAACM,MAAM,CACNN,aAAAA,OAAC,CAACI,MAAM,IACRJ,aAAAA,OAAC,CAACO,IAAI,CAAC,IAAMT;IAEfE,aAAAA,OAAC,CAACQ,QAAQ,CAACR,aAAAA,OAAC,CAACS,QAAQ,CAACT,aAAAA,OAAC,CAACI,MAAM;IAC9BJ,aAAAA,OAAC,CAACQ,QAAQ,CACRR,aAAAA,OAAC,CAACS,QAAQ,CACRT,aAAAA,OAAC,CAACG,KAAK,CAAC;QACNH,aAAAA,OAAC,CAACU,OAAO,CAAC;QACVV,aAAAA,OAAC,CAACU,OAAO,CAAC;QACVV,aAAAA,OAAC,CAACU,OAAO,CAAC;QACVV,aAAAA,OAAC,CAACU,OAAO,CAAC;KACX;IAGLV,aAAAA,OAAC,CAACQ,QAAQ,CAACR,aAAAA,OAAC,CAACW,OAAO;CACrB;AAoDM,IAAWd,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;IAChB,yDAAyD;;IAEzD,mEAAmE;IACnE,gBAAgB;;IAEhB,yEAAyE;;WANzDA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/app-render/parse-and-validate-flight-router-state.tsx"], "sourcesContent": ["import type { FlightRouterState } from './types'\nimport { flightRouterStateSchema } from './types'\nimport { assert } from 'next/dist/compiled/superstruct'\n\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[]\n): FlightRouterState\nexport function parseAndValidateFlightRouterState(\n  stateHeader: undefined\n): undefined\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[] | undefined\n): FlightRouterState | undefined\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[] | undefined\n): FlightRouterState | undefined {\n  if (typeof stateHeader === 'undefined') {\n    return undefined\n  }\n  if (Array.isArray(stateHeader)) {\n    throw new Error(\n      'Multiple router state headers were sent. This is not allowed.'\n    )\n  }\n\n  // We limit the size of the router state header to ~40kb. This is to prevent\n  // a malicious user from sending a very large header and slowing down the\n  // resolving of the router state.\n  // This is around 2,000 nested or parallel route segment states:\n  // '{\"children\":[\"\",{}]}'.length === 20.\n  if (stateHeader.length > 20 * 2000) {\n    throw new Error('The router state header was too large.')\n  }\n\n  try {\n    const state = JSON.parse(decodeURIComponent(stateHeader))\n    assert(state, flightRouterStateSchema)\n    return state\n  } catch {\n    throw new Error('The router state header was sent but could not be parsed.')\n  }\n}\n"], "names": ["parseAndValidateFlightRouterState", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Array", "isArray", "Error", "length", "state", "JSON", "parse", "decodeURIComponent", "assert", "flightRouterStateSchema"], "mappings": ";;;+BAagBA,qCAAAA;;;eAAAA;;;uBAZwB;6BACjB;AAWhB,SAASA,kCACdC,WAA0C;IAE1C,IAAI,OAAOA,gBAAgB,aAAa;QACtC,OAAOC;IACT;IACA,IAAIC,MAAMC,OAAO,CAACH,cAAc;QAC9B,MAAM,OAAA,cAEL,CAFK,IAAII,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4EAA4E;IAC5E,yEAAyE;IACzE,iCAAiC;IACjC,gEAAgE;IAChE,wCAAwC;IACxC,IAAIJ,YAAYK,MAAM,GAAG,KAAK,MAAM;QAClC,MAAM,OAAA,cAAmD,CAAnD,IAAID,MAAM,2CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,IAAI;QACF,MAAME,QAAQC,KAAKC,KAAK,CAACC,mBAAmBT;QAC5CU,CAAAA,GAAAA,aAAAA,MAAM,EAACJ,OAAOK,OAAAA,uBAAuB;QACrC,OAAOL;IACT,EAAE,OAAM;QACN,MAAM,OAAA,cAAsE,CAAtE,IAAIF,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/server-utils.ts"], "sourcesContent": ["import type { Rewrite } from '../lib/load-custom-routes'\nimport type { RouteMatchFn } from '../shared/lib/router/utils/route-matcher'\nimport type { NextConfig } from './config'\nimport type { BaseNextRequest } from './base-http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\n\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { getPathMatch } from '../shared/lib/router/utils/path-match'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { normalizeRscURL } from '../shared/lib/router/utils/app-paths'\nimport {\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../lib/constants'\nimport { normalizeNextQueryParam } from './web/utils'\nimport type { IncomingHttpHeaders, IncomingMessage } from 'http'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { parseReqUrl } from '../lib/url'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport { parseAndValidateFlightRouterState } from './app-render/parse-and-validate-flight-router-state'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport { NEXT_ROUTER_STATE_TREE_HEADER } from '../client/components/app-router-headers'\nimport { getSelectedParams } from '../client/components/router-reducer/compute-changed-path'\n\nfunction filterInternalQuery(\n  query: Record<string, undefined | string | string[]>,\n  paramKeys: string[]\n) {\n  // this is used to pass query information in rewrites\n  // but should not be exposed in final query\n  delete query['nextInternalLocale']\n\n  for (const key in query) {\n    const isNextQueryPrefix =\n      key !== NEXT_QUERY_PARAM_PREFIX && key.startsWith(NEXT_QUERY_PARAM_PREFIX)\n\n    const isNextInterceptionMarkerPrefix =\n      key !== NEXT_INTERCEPTION_MARKER_PREFIX &&\n      key.startsWith(NEXT_INTERCEPTION_MARKER_PREFIX)\n\n    if (\n      isNextQueryPrefix ||\n      isNextInterceptionMarkerPrefix ||\n      paramKeys.includes(key)\n    ) {\n      delete query[key]\n    }\n  }\n}\n\nexport function normalizeCdnUrl(\n  req: BaseNextRequest | IncomingMessage,\n  paramKeys: string[]\n) {\n  // make sure to normalize req.url from CDNs to strip dynamic and rewrite\n  // params from the query which are added during routing\n  const _parsedUrl = parseReqUrl(req.url!)\n\n  // we can't normalize if we can't parse\n  if (!_parsedUrl) {\n    return req.url\n  }\n  delete (_parsedUrl as any).search\n  filterInternalQuery(_parsedUrl.query, paramKeys)\n\n  req.url = formatUrl(_parsedUrl)\n}\n\nexport function interpolateDynamicPath(\n  pathname: string,\n  params: ParsedUrlQuery,\n  defaultRouteRegex?: ReturnType<typeof getNamedRouteRegex> | undefined\n) {\n  if (!defaultRouteRegex) return pathname\n\n  for (const param of Object.keys(defaultRouteRegex.groups)) {\n    const { optional, repeat } = defaultRouteRegex.groups[param]\n    let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n    if (optional) {\n      builtParam = `[${builtParam}]`\n    }\n\n    let paramValue: string\n    const value = params[param]\n\n    if (Array.isArray(value)) {\n      paramValue = value.map((v) => v && encodeURIComponent(v)).join('/')\n    } else if (value) {\n      paramValue = encodeURIComponent(value)\n    } else {\n      paramValue = ''\n    }\n\n    if (paramValue || optional) {\n      pathname = pathname.replaceAll(builtParam, paramValue)\n    }\n  }\n\n  return pathname\n}\n\nexport function normalizeDynamicRouteParams(\n  query: ParsedUrlQuery,\n  defaultRouteRegex: ReturnType<typeof getNamedRouteRegex>,\n  defaultRouteMatches: ParsedUrlQuery,\n  ignoreMissingOptional: boolean\n) {\n  let hasValidParams = true\n  let params: ParsedUrlQuery = {}\n\n  for (const key of Object.keys(defaultRouteRegex.groups)) {\n    let value: string | string[] | undefined = query[key]\n\n    if (typeof value === 'string') {\n      value = normalizeRscURL(value)\n    } else if (Array.isArray(value)) {\n      value = value.map(normalizeRscURL)\n    }\n\n    // if the value matches the default value we can't rely\n    // on the parsed params, this is used to signal if we need\n    // to parse x-now-route-matches or not\n    const defaultValue = defaultRouteMatches![key]\n    const isOptional = defaultRouteRegex!.groups[key].optional\n\n    const isDefaultValue = Array.isArray(defaultValue)\n      ? defaultValue.some((defaultVal) => {\n          return Array.isArray(value)\n            ? value.some((val) => val.includes(defaultVal))\n            : value?.includes(defaultVal)\n        })\n      : value?.includes(defaultValue as string)\n\n    if (\n      isDefaultValue ||\n      (typeof value === 'undefined' && !(isOptional && ignoreMissingOptional))\n    ) {\n      return { params: {}, hasValidParams: false }\n    }\n\n    // non-provided optional values should be undefined so normalize\n    // them to undefined\n    if (\n      isOptional &&\n      (!value ||\n        (Array.isArray(value) &&\n          value.length === 1 &&\n          // fallback optional catch-all SSG pages have\n          // [[...paramName]] for the root path on Vercel\n          (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n    ) {\n      value = undefined\n      delete query[key]\n    }\n\n    // query values from the proxy aren't already split into arrays\n    // so make sure to normalize catch-all values\n    if (\n      value &&\n      typeof value === 'string' &&\n      defaultRouteRegex!.groups[key].repeat\n    ) {\n      value = value.split('/')\n    }\n\n    if (value) {\n      params[key] = value\n    }\n  }\n\n  return {\n    params,\n    hasValidParams,\n  }\n}\n\nexport function getServerUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n  trailingSlash,\n  caseSensitive,\n}: {\n  page: string\n  i18n?: NextConfig['i18n']\n  basePath: string\n  rewrites: DeepReadonly<{\n    fallback?: ReadonlyArray<Rewrite>\n    afterFiles?: ReadonlyArray<Rewrite>\n    beforeFiles?: ReadonlyArray<Rewrite>\n  }>\n  pageIsDynamic: boolean\n  trailingSlash?: boolean\n  caseSensitive: boolean\n}) {\n  let defaultRouteRegex: ReturnType<typeof getNamedRouteRegex> | undefined\n  let dynamicRouteMatcher: RouteMatchFn | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getNamedRouteRegex(page, {\n      prefixRouteKeys: false,\n    })\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(\n    req: BaseNextRequest | IncomingMessage,\n    parsedUrl: UrlWithParsedQuery\n  ) {\n    const rewriteParams: Record<string, string> = {}\n    let fsPathname = parsedUrl.pathname\n\n    const matchesPage = () => {\n      const fsPathnameNoSlash = removeTrailingSlash(fsPathname || '')\n      return (\n        fsPathnameNoSlash === removeTrailingSlash(page) ||\n        dynamicRouteMatcher?.(fsPathnameNoSlash)\n      )\n    }\n\n    const checkRewrite = (rewrite: DeepReadonly<Rewrite>): boolean => {\n      const matcher = getPathMatch(\n        rewrite.source + (trailingSlash ? '(/)?' : ''),\n        {\n          removeUnnamedParams: true,\n          strict: true,\n          sensitive: !!caseSensitive,\n        }\n      )\n\n      if (!parsedUrl.pathname) return false\n\n      let params = matcher(parsedUrl.pathname)\n\n      if ((rewrite.has || rewrite.missing) && params) {\n        const hasParams = matchHas(\n          req,\n          parsedUrl.query,\n          rewrite.has as Rewrite['has'],\n          rewrite.missing as Rewrite['missing']\n        )\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        try {\n          // An interception rewrite might reference a dynamic param for a route the user\n          // is currently on, which wouldn't be extractable from the matched route params.\n          // This attempts to extract the dynamic params from the provided router state.\n          if (isInterceptionRouteRewrite(rewrite as Rewrite)) {\n            const stateHeader = req.headers[NEXT_ROUTER_STATE_TREE_HEADER]\n\n            if (stateHeader) {\n              params = {\n                ...getSelectedParams(\n                  parseAndValidateFlightRouterState(stateHeader)\n                ),\n                ...params,\n              }\n            }\n          }\n        } catch (err) {\n          // this is a no-op -- we couldn't extract dynamic params from the provided router state,\n          // so we'll just use the params from the route matcher\n        }\n\n        const { parsedDestination, destQuery } = prepareDestination({\n          appendParamsToQuery: true,\n          destination: rewrite.destination,\n          params: params,\n          query: parsedUrl.query,\n        })\n\n        // if the rewrite destination is external break rewrite chain\n        if (parsedDestination.protocol) {\n          return true\n        }\n\n        Object.assign(rewriteParams, destQuery, params)\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        // for each property in parsedUrl.query, if the value is parametrized (eg :foo), look up the value\n        // in rewriteParams and replace the parametrized value with the actual value\n        // this is used when the rewrite destination does not contain the original source param\n        // and so the value is still parametrized and needs to be replaced with the actual rewrite param\n        Object.entries(parsedUrl.query).forEach(([key, value]) => {\n          if (value && typeof value === 'string' && value.startsWith(':')) {\n            const paramName = value.slice(1)\n            const actualValue = rewriteParams[paramName]\n            if (actualValue) {\n              parsedUrl.query[key] = actualValue\n            }\n          }\n        })\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        fsPathname = parsedUrl.pathname\n        if (!fsPathname) return false\n\n        if (basePath) {\n          fsPathname = fsPathname.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const result = normalizeLocalePath(fsPathname, i18n.locales)\n          fsPathname = result.pathname\n          parsedUrl.query.nextInternalLocale =\n            result.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          return true\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            return true\n          }\n        }\n      }\n      return false\n    }\n\n    for (const rewrite of rewrites.beforeFiles || []) {\n      checkRewrite(rewrite)\n    }\n\n    if (fsPathname !== page) {\n      let finished = false\n\n      for (const rewrite of rewrites.afterFiles || []) {\n        finished = checkRewrite(rewrite)\n        if (finished) break\n      }\n\n      if (!finished && !matchesPage()) {\n        for (const rewrite of rewrites.fallback || []) {\n          finished = checkRewrite(rewrite)\n          if (finished) break\n        }\n      }\n    }\n    return rewriteParams\n  }\n\n  function getParamsFromRouteMatches(routeMatchesHeader: string) {\n    // If we don't have a default route regex, we can't get params from route\n    // matches\n    if (!defaultRouteRegex) return null\n\n    const { groups, routeKeys } = defaultRouteRegex\n\n    const matcher = getRouteMatcher({\n      re: {\n        // Simulate a RegExp match from the \\`req.url\\` input\n        exec: (str: string) => {\n          // Normalize all the prefixed query params.\n          const obj: Record<string, string> = Object.fromEntries(\n            new URLSearchParams(str)\n          )\n          for (const [key, value] of Object.entries(obj)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            obj[normalizedKey] = value\n            delete obj[key]\n          }\n\n          // Use all the named route keys.\n          const result = {} as RegExpExecArray\n          for (const keyName of Object.keys(routeKeys)) {\n            const paramName = routeKeys[keyName]\n\n            // If this param name is not a valid parameter name, then skip it.\n            if (!paramName) continue\n\n            const group = groups[paramName]\n            const value = obj[keyName]\n\n            // When we're missing a required param, we can't match the route.\n            if (!group.optional && !value) return null\n\n            result[group.pos] = value\n          }\n\n          return result\n        },\n      },\n      groups,\n    })\n\n    const routeMatches = matcher(routeMatchesHeader)\n    if (!routeMatches) return null\n\n    return routeMatches\n  }\n\n  function normalizeQueryParams(\n    query: Record<string, string | string[] | undefined>,\n    routeParamKeys: Set<string>\n  ) {\n    // this is used to pass query information in rewrites\n    // but should not be exposed in final query\n    delete query['nextInternalLocale']\n\n    for (const [key, value] of Object.entries(query)) {\n      const normalizedKey = normalizeNextQueryParam(key)\n      if (!normalizedKey) continue\n\n      // Remove the prefixed key from the query params because we want\n      // to consume it for the dynamic route matcher.\n      delete query[key]\n      routeParamKeys.add(normalizedKey)\n\n      if (typeof value === 'undefined') continue\n\n      query[normalizedKey] = Array.isArray(value)\n        ? value.map((v) => decodeQueryPathParameter(v))\n        : decodeQueryPathParameter(value)\n    }\n  }\n\n  return {\n    handleRewrites,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    normalizeQueryParams,\n    getParamsFromRouteMatches,\n    /**\n     * Normalize dynamic route params.\n     *\n     * @param query - The query params to normalize.\n     * @param ignoreMissingOptional - Whether to ignore missing optional params.\n     * @returns The normalized params and whether they are valid.\n     */\n    normalizeDynamicRouteParams: (\n      query: ParsedUrlQuery,\n      ignoreMissingOptional: boolean\n    ) => {\n      if (!defaultRouteRegex || !defaultRouteMatches) {\n        return { params: {}, hasValidParams: false }\n      }\n\n      return normalizeDynamicRouteParams(\n        query,\n        defaultRouteRegex,\n        defaultRouteMatches,\n        ignoreMissingOptional\n      )\n    },\n\n    normalizeCdnUrl: (\n      req: BaseNextRequest | IncomingMessage,\n      paramKeys: string[]\n    ) => normalizeCdnUrl(req, paramKeys),\n\n    interpolateDynamicPath: (\n      pathname: string,\n      params: Record<string, undefined | string | string[]>\n    ) => interpolateDynamicPath(pathname, params, defaultRouteRegex),\n\n    filterInternalQuery: (query: ParsedUrlQuery, paramKeys: string[]) =>\n      filterInternalQuery(query, paramKeys),\n  }\n}\n\nexport function getPreviouslyRevalidatedTags(\n  headers: IncomingHttpHeaders,\n  previewModeId: string | undefined\n): string[] {\n  return typeof headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER] === 'string' &&\n    headers[NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER] === previewModeId\n    ? headers[NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(',')\n    : []\n}\n"], "names": ["getPreviouslyRevalidatedTags", "getServerUtils", "interpolateDynamicPath", "normalizeCdnUrl", "normalizeDynamicRouteParams", "filterInternalQuery", "query", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "isNextQueryPrefix", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "isNextInterceptionMarkerPrefix", "NEXT_INTERCEPTION_MARKER_PREFIX", "includes", "req", "_parsedUrl", "parseReqUrl", "url", "search", "formatUrl", "pathname", "params", "defaultRouteRegex", "param", "Object", "keys", "groups", "optional", "repeat", "builtParam", "paramValue", "value", "Array", "isArray", "map", "v", "encodeURIComponent", "join", "replaceAll", "defaultRouteMatches", "ignoreMissingOptional", "hasValidParams", "normalizeRscURL", "defaultValue", "isOptional", "isDefaultValue", "some", "defaultVal", "val", "length", "undefined", "split", "page", "i18n", "basePath", "rewrites", "pageIsDynamic", "trailingSlash", "caseSensitive", "dynamicRouteMatcher", "getNamedRouteRegex", "prefixRouteKeys", "getRouteMatcher", "handleRewrites", "parsedUrl", "rewriteParams", "fsPathname", "matchesPage", "fsPathnameNoSlash", "removeTrailingSlash", "checkRewrite", "rewrite", "matcher", "getPathMatch", "source", "removeUnnamedP<PERSON>ms", "strict", "sensitive", "has", "missing", "hasParams", "matchHas", "assign", "isInterceptionRouteRewrite", "<PERSON><PERSON><PERSON><PERSON>", "headers", "NEXT_ROUTER_STATE_TREE_HEADER", "getSelectedParams", "parseAndValidateFlightRouterState", "err", "parsedDestination", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareDestination", "appendParamsToQuery", "destination", "protocol", "entries", "for<PERSON>ach", "paramName", "slice", "actualValue", "replace", "RegExp", "result", "normalizeLocalePath", "locales", "nextInternalLocale", "detectedLocale", "dynamicParams", "beforeFiles", "finished", "afterFiles", "fallback", "getParamsFromRouteMatches", "routeMatchesHeader", "routeKeys", "re", "exec", "str", "obj", "fromEntries", "URLSearchParams", "normalizedKey", "normalizeNextQueryParam", "keyName", "group", "pos", "routeMatches", "normalizeQueryParams", "routeParamKeys", "add", "decodeQueryPathParameter", "previewModeId", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;IA8egBA,4BAA4B,EAAA;eAA5BA;;IAnTAC,cAAc,EAAA;eAAdA;;IA7GAC,sBAAsB,EAAA;eAAtBA;;IAlBAC,eAAe,EAAA;eAAfA;;IAoDAC,2BAA2B,EAAA;eAA3BA;;;qCAzGoB;2BACP;4BACM;8BACH;oCAIzB;qCAC6B;0BACJ;2BAMzB;uBACiC;0CAEC;qBAEb;2BACF;mDACwB;oDACP;kCACG;oCACZ;AAElC,SAASC,oBACPC,KAAoD,EACpDC,SAAmB;IAEnB,qDAAqD;IACrD,2CAA2C;IAC3C,OAAOD,KAAK,CAAC,qBAAqB;IAElC,IAAK,MAAME,OAAOF,MAAO;QACvB,MAAMG,oBACJD,QAAQE,WAAAA,uBAAuB,IAAIF,IAAIG,UAAU,CAACD,WAAAA,uBAAuB;QAE3E,MAAME,iCACJJ,QAAQK,WAAAA,+BAA+B,IACvCL,IAAIG,UAAU,CAACE,WAAAA,+BAA+B;QAEhD,IACEJ,qBACAG,kCACAL,UAAUO,QAAQ,CAACN,MACnB;YACA,OAAOF,KAAK,CAACE,IAAI;QACnB;IACF;AACF;AAEO,SAASL,gBACdY,GAAsC,EACtCR,SAAmB;IAEnB,wEAAwE;IACxE,uDAAuD;IACvD,MAAMS,aAAaC,CAAAA,GAAAA,KAAAA,WAAW,EAACF,IAAIG,GAAG;IAEtC,uCAAuC;IACvC,IAAI,CAACF,YAAY;QACf,OAAOD,IAAIG,GAAG;IAChB;IACA,OAAQF,WAAmBG,MAAM;IACjCd,oBAAoBW,WAAWV,KAAK,EAAEC;IAEtCQ,IAAIG,GAAG,GAAGE,CAAAA,GAAAA,WAAAA,SAAS,EAACJ;AACtB;AAEO,SAASd,uBACdmB,QAAgB,EAChBC,MAAsB,EACtBC,iBAAqE;IAErE,IAAI,CAACA,mBAAmB,OAAOF;IAE/B,KAAK,MAAMG,SAASC,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACzD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGN,kBAAkBI,MAAM,CAACH,MAAM;QAC5D,IAAIM,aAAa,CAAC,CAAC,EAAED,SAAS,QAAQ,KAAKL,MAAM,CAAC,CAAC;QAEnD,IAAII,UAAU;YACZE,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC;QAChC;QAEA,IAAIC;QACJ,MAAMC,QAAQV,MAAM,CAACE,MAAM;QAE3B,IAAIS,MAAMC,OAAO,CAACF,QAAQ;YACxBD,aAAaC,MAAMG,GAAG,CAAC,CAACC,IAAMA,KAAKC,mBAAmBD,IAAIE,IAAI,CAAC;QACjE,OAAO,IAAIN,OAAO;YAChBD,aAAaM,mBAAmBL;QAClC,OAAO;YACLD,aAAa;QACf;QAEA,IAAIA,cAAcH,UAAU;YAC1BP,WAAWA,SAASkB,UAAU,CAACT,YAAYC;QAC7C;IACF;IAEA,OAAOV;AACT;AAEO,SAASjB,4BACdE,KAAqB,EACrBiB,iBAAwD,EACxDiB,mBAAmC,EACnCC,qBAA8B;IAE9B,IAAIC,iBAAiB;IACrB,IAAIpB,SAAyB,CAAC;IAE9B,KAAK,MAAMd,OAAOiB,OAAOC,IAAI,CAACH,kBAAkBI,MAAM,EAAG;QACvD,IAAIK,QAAuC1B,KAAK,CAACE,IAAI;QAErD,IAAI,OAAOwB,UAAU,UAAU;YAC7BA,QAAQW,CAAAA,GAAAA,UAAAA,eAAe,EAACX;QAC1B,OAAO,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YAC/BA,QAAQA,MAAMG,GAAG,CAACQ,UAAAA,eAAe;QACnC;QAEA,uDAAuD;QACvD,0DAA0D;QAC1D,sCAAsC;QACtC,MAAMC,eAAeJ,mBAAoB,CAAChC,IAAI;QAC9C,MAAMqC,aAAatB,kBAAmBI,MAAM,CAACnB,IAAI,CAACoB,QAAQ;QAE1D,MAAMkB,iBAAiBb,MAAMC,OAAO,CAACU,gBACjCA,aAAaG,IAAI,CAAC,CAACC;YACjB,OAAOf,MAAMC,OAAO,CAACF,SACjBA,MAAMe,IAAI,CAAC,CAACE,MAAQA,IAAInC,QAAQ,CAACkC,eACjChB,SAAAA,OAAAA,KAAAA,IAAAA,MAAOlB,QAAQ,CAACkC;QACtB,KACAhB,SAAAA,OAAAA,KAAAA,IAAAA,MAAOlB,QAAQ,CAAC8B;QAEpB,IACEE,kBACC,OAAOd,UAAU,eAAe,CAAEa,CAAAA,cAAcJ,qBAAoB,GACrE;YACA,OAAO;gBAAEnB,QAAQ,CAAC;gBAAGoB,gBAAgB;YAAM;QAC7C;QAEA,gEAAgE;QAChE,oBAAoB;QACpB,IACEG,cACC,CAAA,CAACb,SACCC,MAAMC,OAAO,CAACF,UACbA,MAAMkB,MAAM,KAAK,KACjB,6CAA6C;QAC7C,+CAA+C;QAC9ClB,CAAAA,KAAK,CAAC,EAAE,KAAK,WAAWA,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAExB,IAAI,EAAE,CAAA,CAAE,GAC1D;YACAwB,QAAQmB;YACR,OAAO7C,KAAK,CAACE,IAAI;QACnB;QAEA,+DAA+D;QAC/D,6CAA6C;QAC7C,IACEwB,SACA,OAAOA,UAAU,YACjBT,kBAAmBI,MAAM,CAACnB,IAAI,CAACqB,MAAM,EACrC;YACAG,QAAQA,MAAMoB,KAAK,CAAC;QACtB;QAEA,IAAIpB,OAAO;YACTV,MAAM,CAACd,IAAI,GAAGwB;QAChB;IACF;IAEA,OAAO;QACLV;QACAoB;IACF;AACF;AAEO,SAASzC,eAAe,EAC7BoD,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,aAAa,EAad;IACC,IAAIpC;IACJ,IAAIqC;IACJ,IAAIpB;IAEJ,IAAIiB,eAAe;QACjBlC,oBAAoBsC,CAAAA,GAAAA,YAAAA,kBAAkB,EAACR,MAAM;YAC3CS,iBAAiB;QACnB;QACAF,sBAAsBG,CAAAA,GAAAA,cAAAA,eAAe,EAACxC;QACtCiB,sBAAsBoB,oBAAoBP;IAC5C;IAEA,SAASW,eACPjD,GAAsC,EACtCkD,SAA6B;QAE7B,MAAMC,gBAAwC,CAAC;QAC/C,IAAIC,aAAaF,UAAU5C,QAAQ;QAEnC,MAAM+C,cAAc;YAClB,MAAMC,oBAAoBC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACH,cAAc;YAC5D,OACEE,sBAAsBC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACjB,SAAAA,CAC1CO,uBAAAA,OAAAA,KAAAA,IAAAA,oBAAsBS,kBAAAA;QAE1B;QAEA,MAAME,eAAe,CAACC;YACpB,MAAMC,UAAUC,CAAAA,GAAAA,WAAAA,YAAY,EAC1BF,QAAQG,MAAM,GAAIjB,CAAAA,gBAAgB,SAAS,EAAC,GAC5C;gBACEkB,qBAAqB;gBACrBC,QAAQ;gBACRC,WAAW,CAAC,CAACnB;YACf;YAGF,IAAI,CAACM,UAAU5C,QAAQ,EAAE,OAAO;YAEhC,IAAIC,SAASmD,QAAQR,UAAU5C,QAAQ;YAEvC,IAAKmD,CAAAA,QAAQO,GAAG,IAAIP,QAAQQ,OAAM,KAAM1D,QAAQ;gBAC9C,MAAM2D,YAAYC,CAAAA,GAAAA,oBAAAA,QAAQ,EACxBnE,KACAkD,UAAU3D,KAAK,EACfkE,QAAQO,GAAG,EACXP,QAAQQ,OAAO;gBAGjB,IAAIC,WAAW;oBACbxD,OAAO0D,MAAM,CAAC7D,QAAQ2D;gBACxB,OAAO;oBACL3D,SAAS;gBACX;YACF;YAEA,IAAIA,QAAQ;gBACV,IAAI;oBACF,+EAA+E;oBAC/E,gFAAgF;oBAChF,8EAA8E;oBAC9E,IAAI8D,CAAAA,GAAAA,oCAAAA,0BAA0B,EAACZ,UAAqB;wBAClD,MAAMa,cAActE,IAAIuE,OAAO,CAACC,kBAAAA,6BAA6B,CAAC;wBAE9D,IAAIF,aAAa;4BACf/D,SAAS;gCACP,GAAGkE,CAAAA,GAAAA,oBAAAA,iBAAiB,EAClBC,CAAAA,GAAAA,mCAAAA,iCAAiC,EAACJ,aACnC;gCACD,GAAG/D,MAAM;4BACX;wBACF;oBACF;gBACF,EAAE,OAAOoE,KAAK;gBACZ,wFAAwF;gBACxF,sDAAsD;gBACxD;gBAEA,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAE,GAAGC,CAAAA,GAAAA,oBAAAA,kBAAkB,EAAC;oBAC1DC,qBAAqB;oBACrBC,aAAavB,QAAQuB,WAAW;oBAChCzE,QAAQA;oBACRhB,OAAO2D,UAAU3D,KAAK;gBACxB;gBAEA,6DAA6D;gBAC7D,IAAIqF,kBAAkBK,QAAQ,EAAE;oBAC9B,OAAO;gBACT;gBAEAvE,OAAO0D,MAAM,CAACjB,eAAe0B,WAAWtE;gBACxCG,OAAO0D,MAAM,CAAClB,UAAU3D,KAAK,EAAEqF,kBAAkBrF,KAAK;gBACtD,OAAQqF,kBAA0BrF,KAAK;gBAEvC,kGAAkG;gBAClG,4EAA4E;gBAC5E,uFAAuF;gBACvF,gGAAgG;gBAChGmB,OAAOwE,OAAO,CAAChC,UAAU3D,KAAK,EAAE4F,OAAO,CAAC,CAAC,CAAC1F,KAAKwB,MAAM;oBACnD,IAAIA,SAAS,OAAOA,UAAU,YAAYA,MAAMrB,UAAU,CAAC,MAAM;wBAC/D,MAAMwF,YAAYnE,MAAMoE,KAAK,CAAC;wBAC9B,MAAMC,cAAcnC,aAAa,CAACiC,UAAU;wBAC5C,IAAIE,aAAa;4BACfpC,UAAU3D,KAAK,CAACE,IAAI,GAAG6F;wBACzB;oBACF;gBACF;gBAEA5E,OAAO0D,MAAM,CAAClB,WAAW0B;gBAEzBxB,aAAaF,UAAU5C,QAAQ;gBAC/B,IAAI,CAAC8C,YAAY,OAAO;gBAExB,IAAIZ,UAAU;oBACZY,aAAaA,WAAWmC,OAAO,CAAC,IAAIC,OAAO,CAAC,CAAC,EAAEhD,UAAU,GAAG,OAAO;gBACrE;gBAEA,IAAID,MAAM;oBACR,MAAMkD,SAASC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACtC,YAAYb,KAAKoD,OAAO;oBAC3DvC,aAAaqC,OAAOnF,QAAQ;oBAC5B4C,UAAU3D,KAAK,CAACqG,kBAAkB,GAChCH,OAAOI,cAAc,IAAItF,OAAOqF,kBAAkB;gBACtD;gBAEA,IAAIxC,eAAed,MAAM;oBACvB,OAAO;gBACT;gBAEA,IAAII,iBAAiBG,qBAAqB;oBACxC,MAAMiD,gBAAgBjD,oBAAoBO;oBAC1C,IAAI0C,eAAe;wBACjB5C,UAAU3D,KAAK,GAAG;4BAChB,GAAG2D,UAAU3D,KAAK;4BAClB,GAAGuG,aAAa;wBAClB;wBACA,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,KAAK,MAAMrC,WAAWhB,SAASsD,WAAW,IAAI,EAAE,CAAE;YAChDvC,aAAaC;QACf;QAEA,IAAIL,eAAed,MAAM;YACvB,IAAI0D,WAAW;YAEf,KAAK,MAAMvC,WAAWhB,SAASwD,UAAU,IAAI,EAAE,CAAE;gBAC/CD,WAAWxC,aAAaC;gBACxB,IAAIuC,UAAU;YAChB;YAEA,IAAI,CAACA,YAAY,CAAC3C,eAAe;gBAC/B,KAAK,MAAMI,WAAWhB,SAASyD,QAAQ,IAAI,EAAE,CAAE;oBAC7CF,WAAWxC,aAAaC;oBACxB,IAAIuC,UAAU;gBAChB;YACF;QACF;QACA,OAAO7C;IACT;IAEA,SAASgD,0BAA0BC,kBAA0B;QAC3D,yEAAyE;QACzE,UAAU;QACV,IAAI,CAAC5F,mBAAmB,OAAO;QAE/B,MAAM,EAAEI,MAAM,EAAEyF,SAAS,EAAE,GAAG7F;QAE9B,MAAMkD,UAAUV,CAAAA,GAAAA,cAAAA,eAAe,EAAC;YAC9BsD,IAAI;gBACF,qDAAqD;gBACrDC,MAAM,CAACC;oBACL,2CAA2C;oBAC3C,MAAMC,MAA8B/F,OAAOgG,WAAW,CACpD,IAAIC,gBAAgBH;oBAEtB,KAAK,MAAM,CAAC/G,KAAKwB,MAAM,IAAIP,OAAOwE,OAAO,CAACuB,KAAM;wBAC9C,MAAMG,gBAAgBC,CAAAA,GAAAA,OAAAA,uBAAuB,EAACpH;wBAC9C,IAAI,CAACmH,eAAe;wBAEpBH,GAAG,CAACG,cAAc,GAAG3F;wBACrB,OAAOwF,GAAG,CAAChH,IAAI;oBACjB;oBAEA,gCAAgC;oBAChC,MAAMgG,SAAS,CAAC;oBAChB,KAAK,MAAMqB,WAAWpG,OAAOC,IAAI,CAAC0F,WAAY;wBAC5C,MAAMjB,YAAYiB,SAAS,CAACS,QAAQ;wBAEpC,kEAAkE;wBAClE,IAAI,CAAC1B,WAAW;wBAEhB,MAAM2B,QAAQnG,MAAM,CAACwE,UAAU;wBAC/B,MAAMnE,QAAQwF,GAAG,CAACK,QAAQ;wBAE1B,iEAAiE;wBACjE,IAAI,CAACC,MAAMlG,QAAQ,IAAI,CAACI,OAAO,OAAO;wBAEtCwE,MAAM,CAACsB,MAAMC,GAAG,CAAC,GAAG/F;oBACtB;oBAEA,OAAOwE;gBACT;YACF;YACA7E;QACF;QAEA,MAAMqG,eAAevD,QAAQ0C;QAC7B,IAAI,CAACa,cAAc,OAAO;QAE1B,OAAOA;IACT;IAEA,SAASC,qBACP3H,KAAoD,EACpD4H,cAA2B;QAE3B,qDAAqD;QACrD,2CAA2C;QAC3C,OAAO5H,KAAK,CAAC,qBAAqB;QAElC,KAAK,MAAM,CAACE,KAAKwB,MAAM,IAAIP,OAAOwE,OAAO,CAAC3F,OAAQ;YAChD,MAAMqH,gBAAgBC,CAAAA,GAAAA,OAAAA,uBAAuB,EAACpH;YAC9C,IAAI,CAACmH,eAAe;YAEpB,gEAAgE;YAChE,+CAA+C;YAC/C,OAAOrH,KAAK,CAACE,IAAI;YACjB0H,eAAeC,GAAG,CAACR;YAEnB,IAAI,OAAO3F,UAAU,aAAa;YAElC1B,KAAK,CAACqH,cAAc,GAAG1F,MAAMC,OAAO,CAACF,SACjCA,MAAMG,GAAG,CAAC,CAACC,IAAMgG,CAAAA,GAAAA,0BAAAA,wBAAwB,EAAChG,MAC1CgG,CAAAA,GAAAA,0BAAAA,wBAAwB,EAACpG;QAC/B;IACF;IAEA,OAAO;QACLgC;QACAzC;QACAqC;QACApB;QACAyF;QACAf;QACA;;;;;;KAMC,GACD9G,6BAA6B,CAC3BE,OACAmC;YAEA,IAAI,CAAClB,qBAAqB,CAACiB,qBAAqB;gBAC9C,OAAO;oBAAElB,QAAQ,CAAC;oBAAGoB,gBAAgB;gBAAM;YAC7C;YAEA,OAAOtC,4BACLE,OACAiB,mBACAiB,qBACAC;QAEJ;QAEAtC,iBAAiB,CACfY,KACAR,YACGJ,gBAAgBY,KAAKR;QAE1BL,wBAAwB,CACtBmB,UACAC,SACGpB,uBAAuBmB,UAAUC,QAAQC;QAE9ClB,qBAAqB,CAACC,OAAuBC,YAC3CF,oBAAoBC,OAAOC;IAC/B;AACF;AAEO,SAASP,6BACdsF,OAA4B,EAC5B+C,aAAiC;IAEjC,OAAO,OAAO/C,OAAO,CAACgD,WAAAA,kCAAkC,CAAC,KAAK,YAC5DhD,OAAO,CAACiD,WAAAA,sCAAsC,CAAC,KAAKF,gBAClD/C,OAAO,CAACgD,WAAAA,kCAAkC,CAAC,CAAClF,KAAK,CAAC,OAClD,EAAE;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMTurbopackServer\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackServer"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qOACRC,QAAQ,CAAC,YAAY,CAAEC,6BAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-static.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMTurbopackStatic\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackStatic"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qOACRC,QAAQ,CAAC,YAAY,CAAEC,6BAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qOACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qOACRC,QAAQ,CAAC,YAAY,CAAEC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-dom.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,qOACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'rsc' as const\nexport const ACTION_HEADER = 'next-action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'next-router-state-tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'next-router-prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change next-router-state-tree to be a segment path, we can use\n// that instead. Then next-router-prefetch and next-router-segment-prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'next-router-segment-prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'next-hmr-refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'next-url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\nexport const NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found' as const\n"], "names": ["ACTION_HEADER", "FLIGHT_HEADERS", "NEXT_ACTION_NOT_FOUND_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_HMR_REFRESH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "RSC_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IACaA,aAAa,EAAA;eAAbA;;IAiBAC,cAAc,EAAA;eAAdA;;IAeAC,4BAA4B,EAAA;eAA5BA;;IAJAC,wBAAwB,EAAA;eAAxBA;;IAfAC,4BAA4B,EAAA;eAA5BA;;IADAC,uBAAuB,EAAA;eAAvBA;;IAmBAC,wBAAwB,EAAA;eAAxBA;;IAFAC,0BAA0B,EAAA;eAA1BA;;IACAC,2BAA2B,EAAA;eAA3BA;;IAzBAC,2BAA2B,EAAA;eAA3BA;;IAKAC,mCAAmC,EAAA;eAAnCA;;IAiBAC,6BAA6B,EAAA;eAA7BA;;IAvBAC,6BAA6B,EAAA;eAA7BA;;IAqBAC,oBAAoB,EAAA;eAApBA;;IAXAC,QAAQ,EAAA;eAARA;;IACAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,UAAU,EAAA;eAAVA;;;AAAN,MAAMA,aAAa;AACnB,MAAMhB,gBAAgB;AAItB,MAAMY,gCAAgC;AACtC,MAAMH,8BAA8B;AAKpC,MAAMC,sCACX;AACK,MAAML,0BAA0B;AAChC,MAAMD,+BAA+B;AACrC,MAAMU,WAAW;AACjB,MAAMC,0BAA0B;AAEhC,MAAMd,iBAAiB;IAC5Be;IACAJ;IACAH;IACAJ;IACAK;CACD;AAEM,MAAMG,uBAAuB;AAE7B,MAAMF,gCAAgC;AACtC,MAAMR,2BAA2B;AACjC,MAAMI,6BAA6B;AACnC,MAAMC,8BAA8B;AACpC,MAAMF,2BAA2B;AACjC,MAAMJ,+BAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;+BAEaA,gBAAAA;;;eAAAA;;;AAAN,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/router-reducer/compute-changed-path.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n"], "names": ["computeChangedPath", "extractPathFromFlightRouterState", "getSelectedParams", "removeLeadingSlash", "segment", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "isGroupSegment", "flightRouterState", "Array", "isArray", "DEFAULT_SEGMENT_KEY", "INTERCEPTION_ROUTE_MARKERS", "some", "m", "startsWith", "undefined", "PAGE_SEGMENT_KEY", "parallelRoutes", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "Object", "entries", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "matchSegment", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "split", "currentTree", "params", "parallelRoute", "values", "isDynamicParameter", "segmentValue", "isCatchAll"], "mappings": ";;;;;;;;;;;;;;;IAwHgBA,kBAAkB,EAAA;eAAlBA;;IA9EAC,gCAAgC,EAAA;eAAhCA;;IA+FAC,iBAAiB,EAAA;eAAjBA;;;oCArI2B;yBAMpC;+BACsB;AAE7B,MAAMC,qBAAqB,CAACC;IAC1B,OAAOA,OAAO,CAAC,EAAE,KAAK,MAAMA,QAAQC,KAAK,CAAC,KAAKD;AACjD;AAEA,MAAME,oBAAoB,CAACF;IACzB,IAAI,OAAOA,YAAY,UAAU;QAC/B,uHAAuH;QACvH,gHAAgH;QAChH,IAAIA,YAAY,YAAY,OAAO;QAEnC,OAAOA;IACT;IAEA,OAAOA,OAAO,CAAC,EAAE;AACnB;AAEA,SAASG,kBAAkBC,QAAkB;IAC3C,OACEA,SAASC,MAAM,CAAC,CAACC,KAAKN;QACpBA,UAAUD,mBAAmBC;QAC7B,IAAIA,YAAY,MAAMO,CAAAA,GAAAA,SAAAA,cAAc,EAACP,UAAU;YAC7C,OAAOM;QACT;QAEA,OAAUA,MAAI,MAAGN;IACnB,GAAG,OAAO;AAEd;AAEO,SAASH,iCACdW,iBAAoC;IAEpC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,iBAAiB,CAAC,EAAE,IAC9CA,iBAAiB,CAAC,EAAE,CAAC,EAAE,GACvBA,iBAAiB,CAAC,EAAE;IAExB,IACER,YAAYW,SAAAA,mBAAmB,IAC/BC,oBAAAA,0BAA0B,CAACC,IAAI,CAAC,CAACC,IAAMd,QAAQe,UAAU,CAACD,KAE1D,OAAOE;IAET,IAAIhB,QAAQe,UAAU,CAACE,SAAAA,gBAAgB,GAAG,OAAO;IAEjD,MAAMb,WAAW;QAACF,kBAAkBF;KAAS;QACtBQ;IAAvB,MAAMU,iBAAiBV,CAAAA,sBAAAA,iBAAiB,CAAC,EAAE,KAAA,OAApBA,sBAAwB,CAAC;IAEhD,MAAMW,eAAeD,eAAeE,QAAQ,GACxCvB,iCAAiCqB,eAAeE,QAAQ,IACxDJ;IAEJ,IAAIG,iBAAiBH,WAAW;QAC9BZ,SAASiB,IAAI,CAACF;IAChB,OAAO;QACL,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACP,gBAAiB;YACzD,IAAII,QAAQ,YAAY;YAExB,MAAMI,YAAY7B,iCAAiC0B;YAEnD,IAAIG,cAAcV,WAAW;gBAC3BZ,SAASiB,IAAI,CAACK;YAChB;QACF;IACF;IAEA,OAAOvB,kBAAkBC;AAC3B;AAEA,SAASuB,uBACPC,KAAwB,EACxBC,KAAwB;IAExB,MAAM,CAACC,UAAUC,gBAAgB,GAAGH;IACpC,MAAM,CAACI,UAAUC,gBAAgB,GAAGJ;IAEpC,MAAMK,qBAAqBhC,kBAAkB4B;IAC7C,MAAMK,qBAAqBjC,kBAAkB8B;IAE7C,IACEpB,oBAAAA,0BAA0B,CAACC,IAAI,CAC7B,CAACC,IACCoB,mBAAmBnB,UAAU,CAACD,MAAMqB,mBAAmBpB,UAAU,CAACD,KAEtE;QACA,OAAO;IACT;IAEA,IAAI,CAACsB,CAAAA,GAAAA,eAAAA,YAAY,EAACN,UAAUE,WAAW;YAE9BnC;QADP,8FAA8F;QAC9F,OAAOA,CAAAA,oCAAAA,iCAAiCgC,MAAAA,KAAAA,OAAjChC,oCAA2C;IACpD;IAEA,IAAK,MAAMwC,qBAAqBN,gBAAiB;QAC/C,IAAIE,eAAe,CAACI,kBAAkB,EAAE;YACtC,MAAMC,cAAcX,uBAClBI,eAAe,CAACM,kBAAkB,EAClCJ,eAAe,CAACI,kBAAkB;YAEpC,IAAIC,gBAAgB,MAAM;gBACxB,OAAUpC,kBAAkB8B,YAAU,MAAGM;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS1C,mBACdgC,KAAwB,EACxBC,KAAwB;IAExB,MAAMS,cAAcX,uBAAuBC,OAAOC;IAElD,IAAIS,eAAe,QAAQA,gBAAgB,KAAK;QAC9C,OAAOA;IACT;IAEA,mDAAmD;IACnD,OAAOnC,kBAAkBmC,YAAYC,KAAK,CAAC;AAC7C;AAKO,SAASzC,kBACd0C,WAA8B,EAC9BC,MAAmB;IAAnBA,IAAAA,WAAAA,KAAAA,GAAAA,SAAiB,CAAC;IAElB,MAAMvB,iBAAiBsB,WAAW,CAAC,EAAE;IAErC,KAAK,MAAME,iBAAiBlB,OAAOmB,MAAM,CAACzB,gBAAiB;QACzD,MAAMlB,UAAU0C,aAAa,CAAC,EAAE;QAChC,MAAME,qBAAqBnC,MAAMC,OAAO,CAACV;QACzC,MAAM6C,eAAeD,qBAAqB5C,OAAO,CAAC,EAAE,GAAGA;QACvD,IAAI,CAAC6C,gBAAgBA,aAAa9B,UAAU,CAACE,SAAAA,gBAAgB,GAAG;QAEhE,iEAAiE;QACjE,MAAM6B,aACJF,sBAAuB5C,CAAAA,OAAO,CAAC,EAAE,KAAK,OAAOA,OAAO,CAAC,EAAE,KAAK,IAAG;QAEjE,IAAI8C,YAAY;YACdL,MAAM,CAACzC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE,CAACuC,KAAK,CAAC;QACxC,OAAO,IAAIK,oBAAoB;YAC7BH,MAAM,CAACzC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE;QACjC;QAEAyC,SAAS3C,kBAAkB4C,eAAeD;IAC5C;IAEA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/global-error.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/global-error.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/global-error.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/client/components/builtin/global-error.js\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/node_modules/.pnpm/next%4015.5.2_%40babel%2Bcore%407.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/builtin/global-error.tsx"], "sourcesContent": ["'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n"], "names": ["styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "HandleISRError", "div", "style", "h2", "window", "location", "hostname", "p"], "mappings": "", "ignoreList": [0], "debugId": null}}]}