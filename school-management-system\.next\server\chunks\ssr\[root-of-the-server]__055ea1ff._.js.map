{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuU,GACpW,qGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/school-management-system/src/components/layout/dashboard-layout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline: \"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,+XAAC;QAAI,WAAW,IAAA,2JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/students/%5Bid%5D/page.tsx"], "sourcesContent": ["import { getServerSession } from 'next-auth';\nimport { redirect, notFound } from 'next/navigation';\nimport { authOptions } from '@/lib/auth';\nimport { prisma as db } from '@/lib/db';\nimport { hasPermission } from '@/lib/rbac';\nimport DashboardLayout from '@/components/layout/dashboard-layout';\nimport { adminNavigation } from '@/lib/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  User, \n  Mail, \n  Phone, \n  Calendar, \n  MapPin, \n  BookOpen, \n  Edit, \n  ArrowLeft,\n  GraduationCap,\n  Users,\n  Clock,\n  Award\n} from 'lucide-react';\nimport Link from 'next/link';\n\ninterface StudentDetailsPageProps {\n  params: Promise<{\n    id: string;\n  }>;\n}\n\nexport default async function StudentDetailsPage({ params }: StudentDetailsPageProps) {\n  // Temporarily bypass authentication for testing\n  // const session = await getServerSession(authOptions);\n\n  // if (!session?.user) {\n  //   redirect('/login');\n  // }\n\n  // if (!hasPermission(session.user.role as any, 'students:read')) {\n  //   redirect('/unauthorized');\n  // }\n\n  const { id } = await params;\n\n  // Fetch student with all related data\n  const student = await db.student.findUnique({\n    where: { id },\n    include: {\n      user: true,\n      currentClass: {\n        include: {\n          sections: true,\n        },\n      },\n      currentSection: true,\n      enrollments: {\n        include: {\n          class: true,\n          section: true,\n        },\n      },\n      attendances: {\n        take: 10,\n        orderBy: { date: 'desc' },\n      },\n      marks: {\n        include: {\n          exam: {\n            include: {\n              subject: true,\n            },\n          },\n        },\n        take: 20,\n        orderBy: { createdAt: 'desc' },\n      },\n    },\n  });\n\n  if (!student) {\n    notFound();\n  }\n\n  const formatDate = (date: Date) => {\n    return new Date(date).toLocaleDateString();\n  };\n\n  const getGenderLabel = (gender: string) => {\n    switch (gender) {\n      case 'MALE': return 'Male';\n      case 'FEMALE': return 'Female';\n      case 'OTHER': return 'Other';\n      default: return gender;\n    }\n  };\n\n  const getAttendanceStatus = (status: string) => {\n    switch (status) {\n      case 'PRESENT': return { label: 'Present', color: 'bg-green-100 text-green-800' };\n      case 'ABSENT': return { label: 'Absent', color: 'bg-red-100 text-red-800' };\n      case 'LATE': return { label: 'Late', color: 'bg-yellow-100 text-yellow-800' };\n      case 'HALF_DAY': return { label: 'Half Day', color: 'bg-orange-100 text-orange-800' };\n      default: return { label: status, color: 'bg-gray-100 text-gray-800' };\n    }\n  };\n\n  const calculateAge = (dateOfBirth: Date) => {\n    const today = new Date();\n    const birthDate = new Date(dateOfBirth);\n    let age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    \n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n      age--;\n    }\n    \n    return age;\n  };\n\n  const attendanceStats = student.attendances.reduce((acc, record) => {\n    acc[record.status] = (acc[record.status] || 0) + 1;\n    return acc;\n  }, {} as Record<string, number>);\n\n  const totalAttendance = student.attendances.length;\n  const presentCount = attendanceStats['PRESENT'] || 0;\n  const attendancePercentage = totalAttendance > 0 ? Math.round((presentCount / totalAttendance) * 100) : 0;\n\n  return (\n    <DashboardLayout \n      title=\"Student Details\"\n      navigation={adminNavigation}\n    >\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <Link href=\"/admin/students\">\n              <Button variant=\"ghost\" size=\"sm\">\n                <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                Back to Students\n              </Button>\n            </Link>\n            <div>\n              <h1 className=\"text-3xl font-bold tracking-tight\">\n                {student.user.firstName} {student.user.lastName}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Student ID: {student.id}\n              </p>\n            </div>\n          </div>\n          <Link href={`/admin/students/${student.id}/edit`}>\n            <Button>\n              <Edit className=\"w-4 h-4 mr-2\" />\n              Edit Student\n            </Button>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Personal Information */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <User className=\"w-5 h-5\" />\n                  Personal Information\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Full Name</label>\n                    <p className=\"text-lg font-medium\">\n                      {student.user.firstName} {student.user.lastName}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Email</label>\n                    <p className=\"text-lg font-medium\">\n                      {student.user.email}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Date of Birth</label>\n                    <p className=\"text-lg font-medium\">\n                      {formatDate(student.dob)} ({calculateAge(student.dob)} years old)\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Gender</label>\n                    <p className=\"text-lg font-medium\">\n                      {getGenderLabel(student.gender)}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Phone Number</label>\n                    <p className=\"text-lg font-medium\">\n                      {student.user.phone || 'Not provided'}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Address</label>\n                    <p className=\"text-lg font-medium\">\n                      {student.address || 'Not provided'}\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Academic Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <GraduationCap className=\"w-5 h-5\" />\n                  Academic Information\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Current Class</label>\n                    <p className=\"flex items-center gap-2\">\n                      <BookOpen className=\"w-4 h-4\" />\n                      {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}\n                    </p>\n                  </div>\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Enrolled Subjects</label>\n                    <p className=\"flex items-center gap-2\">\n                      <Users className=\"w-4 h-4\" />\n                      {student.enrollments.length} subjects\n                    </p>\n                  </div>\n                </div>\n                \n                {student.enrollments.length > 0 && (\n                  <div>\n                    <label className=\"text-sm font-medium text-gray-600\">Enrollment History</label>\n                    <div className=\"flex flex-wrap gap-2 mt-2\">\n                      {student.enrollments.map((enrollment) => (\n                        <Badge key={enrollment.id} variant=\"secondary\">\n                          {enrollment.class.name} - {enrollment.section.name}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Recent Marks */}\n            {student.marks.length > 0 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Award className=\"w-5 h-5\" />\n                    Recent Marks\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {student.marks.slice(0, 5).map((mark) => (\n                      <div key={mark.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                        <div>\n                          <p className=\"font-medium\">{mark.exam.subject.name}</p>\n                          <p className=\"text-sm text-gray-600\">{mark.exam.name}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"font-bold text-lg\">{mark.obtainedMarks}/{mark.exam.maxMarks}</p>\n                          <p className=\"text-sm text-gray-600\">\n                            {Math.round((mark.obtainedMarks / mark.exam.maxMarks) * 100)}%\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Guardian Information */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"text-lg\">Guardian Information</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-2\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-600\">Guardian Name</label>\n                  <p>{student.guardianName}</p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-600\">Guardian Phone</label>\n                  <p className=\"flex items-center gap-2\">\n                    <Phone className=\"w-4 h-4\" />\n                    {student.guardianPhone}\n                  </p>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Attendance Summary */}\n            {student.attendances.length > 0 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Attendance Summary</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-blue-600\">{attendancePercentage}%</div>\n                    <div className=\"text-sm text-gray-600\">Attendance Rate</div>\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm\">Present</span>\n                      <span className=\"font-medium\">{presentCount}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm\">Absent</span>\n                      <span className=\"font-medium\">{attendanceStats['ABSENT'] || 0}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm\">Late</span>\n                      <span className=\"font-medium\">{attendanceStats['LATE'] || 0}</span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-sm\">Total Days</span>\n                      <span className=\"font-medium\">{totalAttendance}</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Recent Attendance */}\n            {student.attendances.length > 0 && (\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Recent Attendance</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-2\">\n                    {student.attendances.filter(record => record?.id).slice(0, 5).map((record) => {\n                      const status = getAttendanceStatus(record.status);\n                      return (\n                        <div key={record.id} className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center gap-2\">\n                            <Clock className=\"w-4 h-4 text-gray-400\" />\n                            <span className=\"text-sm\">{formatDate(record.date)}</span>\n                          </div>\n                          <Badge className={status.color}>\n                            {status.label}\n                          </Badge>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;;;;AAQe,eAAe,mBAAmB,EAAE,MAAM,EAA2B;IAClF,gDAAgD;IAChD,uDAAuD;IAEvD,wBAAwB;IACxB,wBAAwB;IACxB,IAAI;IAEJ,mEAAmE;IACnE,+BAA+B;IAC/B,IAAI;IAEJ,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAErB,sCAAsC;IACtC,MAAM,UAAU,MAAM,4JAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QAC1C,OAAO;YAAE;QAAG;QACZ,SAAS;YACP,MAAM;YACN,cAAc;gBACZ,SAAS;oBACP,UAAU;gBACZ;YACF;YACA,gBAAgB;YAChB,aAAa;gBACX,SAAS;oBACP,OAAO;oBACP,SAAS;gBACX;YACF;YACA,aAAa;gBACX,MAAM;gBACN,SAAS;oBAAE,MAAM;gBAAO;YAC1B;YACA,OAAO;gBACL,SAAS;oBACP,MAAM;wBACJ,SAAS;4BACP,SAAS;wBACX;oBACF;gBACF;gBACA,MAAM;gBACN,SAAS;oBAAE,WAAW;gBAAO;YAC/B;QACF;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,IAAA,kVAAQ;IACV;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB;IAC1C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;oBAAE,OAAO;oBAAW,OAAO;gBAA8B;YAChF,KAAK;gBAAU,OAAO;oBAAE,OAAO;oBAAU,OAAO;gBAA0B;YAC1E,KAAK;gBAAQ,OAAO;oBAAE,OAAO;oBAAQ,OAAO;gBAAgC;YAC5E,KAAK;gBAAY,OAAO;oBAAE,OAAO;oBAAY,OAAO;gBAAgC;YACpF;gBAAS,OAAO;oBAAE,OAAO;oBAAQ,OAAO;gBAA4B;QACtE;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACrD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;QAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;YAC/E;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK;QACvD,GAAG,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI;QACjD,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,kBAAkB,QAAQ,WAAW,CAAC,MAAM;IAClD,MAAM,eAAe,eAAe,CAAC,UAAU,IAAI;IACnD,MAAM,uBAAuB,kBAAkB,IAAI,KAAK,KAAK,CAAC,AAAC,eAAe,kBAAmB,OAAO;IAExG,qBACE,+XAAC,gMAAe;QACd,OAAM;QACN,YAAY,6KAAe;kBAE3B,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,wTAAI;oCAAC,MAAK;8CACT,cAAA,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;;0DAC3B,+XAAC,mVAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI1C,+XAAC;;sDACC,+XAAC;4CAAG,WAAU;;gDACX,QAAQ,IAAI,CAAC,SAAS;gDAAC;gDAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;sDAEjD,+XAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;sCAI7B,+XAAC,wTAAI;4BAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;sCAC9C,cAAA,+XAAC,8KAAM;;kDACL,+XAAC,yUAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAMvC,+XAAC;oBAAI,WAAU;;sCAEb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;;kEACnB,+XAAC,gUAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIhC,+XAAC,iLAAW;4CAAC,WAAU;sDACrB,cAAA,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;;oEACV,QAAQ,IAAI,CAAC,SAAS;oEAAC;oEAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;;;;;;;kEAGnD,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;0EACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;kEAGvB,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;;oEACV,WAAW,QAAQ,GAAG;oEAAE;oEAAG,aAAa,QAAQ,GAAG;oEAAE;;;;;;;;;;;;;kEAG1D,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;0EACV,eAAe,QAAQ,MAAM;;;;;;;;;;;;kEAGlC,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;0EACV,QAAQ,IAAI,CAAC,KAAK,IAAI;;;;;;;;;;;;kEAG3B,+XAAC;;0EACC,+XAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,+XAAC;gEAAE,WAAU;0EACV,QAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ9B,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;;kEACnB,+XAAC,+VAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIzC,+XAAC,iLAAW;4CAAC,WAAU;;8DACrB,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;;8EACC,+XAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,+XAAC;oEAAE,WAAU;;sFACX,+XAAC,gVAAQ;4EAAC,WAAU;;;;;;wEACnB,QAAQ,YAAY,GAAG,GAAG,QAAQ,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,cAAc,EAAE,QAAQ,OAAO,GAAG;;;;;;;;;;;;;sEAGxG,+XAAC;;8EACC,+XAAC;oEAAM,WAAU;8EAAoC;;;;;;8EACrD,+XAAC;oEAAE,WAAU;;sFACX,+XAAC,mUAAK;4EAAC,WAAU;;;;;;wEAChB,QAAQ,WAAW,CAAC,MAAM;wEAAC;;;;;;;;;;;;;;;;;;;gDAKjC,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,+XAAC;;sEACC,+XAAC;4DAAM,WAAU;sEAAoC;;;;;;sEACrD,+XAAC;4DAAI,WAAU;sEACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,+XAAC,4KAAK;oEAAqB,SAAQ;;wEAChC,WAAW,KAAK,CAAC,IAAI;wEAAC;wEAAI,WAAW,OAAO,CAAC,IAAI;;mEADxC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAWpC,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;;kEACnB,+XAAC,mUAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIjC,+XAAC,iLAAW;sDACV,cAAA,+XAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC9B,+XAAC;wDAAkB,WAAU;;0EAC3B,+XAAC;;kFACC,+XAAC;wEAAE,WAAU;kFAAe,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI;;;;;;kFAClD,+XAAC;wEAAE,WAAU;kFAAyB,KAAK,IAAI,CAAC,IAAI;;;;;;;;;;;;0EAEtD,+XAAC;gEAAI,WAAU;;kFACb,+XAAC;wEAAE,WAAU;;4EAAqB,KAAK,aAAa;4EAAC;4EAAE,KAAK,IAAI,CAAC,QAAQ;;;;;;;kFACzE,+XAAC;wEAAE,WAAU;;4EACV,KAAK,KAAK,CAAC,AAAC,KAAK,aAAa,GAAG,KAAK,IAAI,CAAC,QAAQ,GAAI;4EAAK;;;;;;;;;;;;;;uDARzD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAoB7B,+XAAC;4BAAI,WAAU;;8CAEb,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,+XAAC,iLAAW;4CAAC,WAAU;;8DACrB,+XAAC;;sEACC,+XAAC;4DAAM,WAAU;sEAAoC;;;;;;sEACrD,+XAAC;sEAAG,QAAQ,YAAY;;;;;;;;;;;;8DAE1B,+XAAC;;sEACC,+XAAC;4DAAM,WAAU;sEAAoC;;;;;;sEACrD,+XAAC;4DAAE,WAAU;;8EACX,+XAAC,mUAAK;oEAAC,WAAU;;;;;;gEAChB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;gCAO7B,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,+XAAC,iLAAW;4CAAC,WAAU;;8DACrB,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAI,WAAU;;gEAAoC;gEAAqB;;;;;;;sEACxE,+XAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAGzC,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,+XAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEAEjC,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,+XAAC;oEAAK,WAAU;8EAAe,eAAe,CAAC,SAAS,IAAI;;;;;;;;;;;;sEAE9D,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,+XAAC;oEAAK,WAAU;8EAAe,eAAe,CAAC,OAAO,IAAI;;;;;;;;;;;;sEAE5D,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,+XAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQxC,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,+XAAC,0KAAI;;sDACH,+XAAC,gLAAU;sDACT,cAAA,+XAAC,+KAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,+XAAC,iLAAW;sDACV,cAAA,+XAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;oDACjE,MAAM,SAAS,oBAAoB,OAAO,MAAM;oDAChD,qBACE,+XAAC;wDAAoB,WAAU;;0EAC7B,+XAAC;gEAAI,WAAU;;kFACb,+XAAC,mUAAK;wEAAC,WAAU;;;;;;kFACjB,+XAAC;wEAAK,WAAU;kFAAW,WAAW,OAAO,IAAI;;;;;;;;;;;;0EAEnD,+XAAC,4KAAK;gEAAC,WAAW,OAAO,KAAK;0EAC3B,OAAO,KAAK;;;;;;;uDANP,OAAO,EAAE;;;;;gDAUvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}]}