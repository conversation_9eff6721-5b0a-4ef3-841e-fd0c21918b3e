{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/school-management-system_8c1ea602._.js", "server/edge/chunks/[root-of-the-server]__433d4839._.js", "server/edge/chunks/turbopack-school-management-system_edge-wrapper_db311ba0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/teacher/:path*{(\\\\.json)}?", "originalSource": "/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/student/:path*{(\\\\.json)}?", "originalSource": "/student/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/admin/:path*{(\\\\.json)}?", "originalSource": "/api/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/teacher/:path*{(\\\\.json)}?", "originalSource": "/api/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/student/:path*{(\\\\.json)}?", "originalSource": "/api/student/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6Mx7JucFskGuKzFVXA5uK6BSblx0rpaFVfStGUIApds=", "__NEXT_PREVIEW_MODE_ID": "d64e3b2960e7d3246787a5b38b5e1d56", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8a7bc35f409e0517b169990cb6557c474a2101e86ceb4b40f88d1e3dac36d8f5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0fde39bf8531e562ba41962c77526878d8668421a6e3d9dc9613e6645f8d9df8"}}}, "instrumentation": null, "functions": {}}