{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  // This interface extends React.InputHTMLAttributes without adding new properties\r\n}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,8JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB,sLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,4TAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,8UAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,8JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,4TAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/students/student-table.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  Eye, \n  Download, \n  Upload,\n  ChevronLeft,\n  ChevronRight,\n  Loader2\n} from 'lucide-react';\n\ninterface Student {\n  id: string;\n  user: {\n    firstName: string;\n    lastName: string;\n    email: string;\n    phone: string | null;\n  };\n  dob: Date | string;\n  gender: 'MALE' | 'FEMALE' | 'OTHER';\n  address?: string | null;\n  guardianName: string;\n  guardianPhone: string;\n  currentClass?: {\n    id: string;\n    name: string;\n    createdAt?: Date;\n    updatedAt?: Date;\n  } | null;\n  currentSection?: {\n    id: string;\n    name: string;\n  } | null;\n  createdAt: Date | string;\n}\n\ninterface Class {\n  id: string;\n  name: string;\n  sections: {\n    id: string;\n    name: string;\n  }[];\n}\n\ninterface StudentTableProps {\n  students: Student[];\n  classes: Class[];\n  pagination: {\n    page: number;\n    limit: number;\n    totalCount: number;\n    totalPages: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\nexport function StudentTable({ students, classes, pagination }: StudentTableProps) {\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [selectedGender, setSelectedGender] = useState('');\n\n  const handleSearch = () => {\n    const params = new URLSearchParams();\n    if (searchTerm) params.append('search', searchTerm);\n    if (selectedClass) params.append('classId', selectedClass);\n    if (selectedGender) params.append('gender', selectedGender);\n    params.append('page', '1');\n    \n    router.push(`/admin/students?${params.toString()}`);\n  };\n\n  const handlePageChange = (page: number) => {\n    const params = new URLSearchParams();\n    if (searchTerm) params.append('search', searchTerm);\n    if (selectedClass) params.append('classId', selectedClass);\n    if (selectedGender) params.append('gender', selectedGender);\n    params.append('page', page.toString());\n    \n    router.push(`/admin/students?${params.toString()}`);\n  };\n\n  const handleDelete = async (studentId: string, studentName: string) => {\n    if (!confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch(`/api/admin/students/${studentId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        const data = await response.json();\n        throw new Error(data.error || 'Failed to delete student');\n      }\n\n      // Refresh the page to show updated data\n      router.refresh();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to delete student');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExport = () => {\n    const params = new URLSearchParams();\n    if (selectedClass) params.append('classId', selectedClass);\n    \n    const url = `/api/admin/students/bulk?${params.toString()}`;\n    window.open(url, '_blank');\n  };\n\n  const formatDate = (date: Date | string) => {\n    return new Date(date).toLocaleDateString();\n  };\n\n  const getGenderLabel = (gender: string) => {\n    switch (gender) {\n      case 'MALE': return 'Male';\n      case 'FEMALE': return 'Female';\n      case 'OTHER': return 'Other';\n      default: return gender;\n    }\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n          <div>\n            <CardTitle>Students</CardTitle>\n            <CardDescription>\n              Manage student information and records\n            </CardDescription>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button\n              variant=\"outline\"\n              onClick={handleExport}\n              disabled={loading}\n            >\n              <Download className=\"w-4 h-4 mr-2\" />\n              Export\n            </Button>\n            <Button\n              onClick={() => router.push('/admin/students/bulk')}\n              variant=\"outline\"\n              disabled={loading}\n            >\n              <Upload className=\"w-4 h-4 mr-2\" />\n              Import\n            </Button>\n            <Button\n              onClick={() => router.push('/admin/students/new')}\n              disabled={loading}\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Student\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-4\">\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* Search and Filters */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">Search</label>\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <Input\n                placeholder=\"Search students...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n              />\n            </div>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">Class</label>\n            <select\n              value={selectedClass}\n              onChange={(e) => setSelectedClass(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Classes</option>\n              {classes.filter(cls => cls?.id).map((cls) =>\n                cls.sections.filter(section => section?.id).map((section) => (\n                  <option key={`${cls.id}-${section.id}`} value={`${cls.id}-${section.id}`}>\n                    {cls.name} - {section.name}\n                  </option>\n                ))\n              )}\n            </select>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">Gender</label>\n            <select\n              value={selectedGender}\n              onChange={(e) => setSelectedGender(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Genders</option>\n              <option value=\"MALE\">Male</option>\n              <option value=\"FEMALE\">Female</option>\n              <option value=\"OTHER\">Other</option>\n            </select>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">&nbsp;</label>\n            <Button onClick={handleSearch} className=\"w-full\" disabled={loading}>\n              {loading ? (\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n              ) : (\n                <Search className=\"w-4 h-4 mr-2\" />\n              )}\n              Search\n            </Button>\n          </div>\n        </div>\n\n        {/* Students Table - Desktop */}\n        <div className=\"hidden lg:block overflow-x-auto\">\n          <table className=\"w-full border-collapse border border-gray-200\">\n            <thead>\n              <tr className=\"bg-gray-50\">\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Name\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Email\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Class\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Gender\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Phone\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-700\">\n                  Admission Date\n                </th>\n                <th className=\"border border-gray-200 px-4 py-2 text-center text-sm font-medium text-gray-700\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {students.filter(student => student?.id).map((student) => (\n                <tr key={student.id} className=\"hover:bg-gray-50\">\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    <div>\n                      <div className=\"font-medium\">\n                        {student.user.firstName} {student.user.lastName}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        {formatDate(student.dob)}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    {student.user.email}\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    {getGenderLabel(student.gender)}\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    {student.user.phone || '-'}\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    {formatDate(student.createdAt)}\n                  </td>\n                  <td className=\"border border-gray-200 px-4 py-2\">\n                    <div className=\"flex justify-center gap-2\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => router.push(`/admin/students/${student.id}`)}\n                        disabled={loading}\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                      </Button>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => router.push(`/admin/students/${student.id}/edit`)}\n                        disabled={loading}\n                      >\n                        <Edit className=\"w-4 h-4\" />\n                      </Button>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => handleDelete(student.id, `${student.user.firstName} ${student.user.lastName}`)}\n                        disabled={loading}\n                        className=\"text-red-600 hover:text-red-700\"\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </Button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Students Cards - Mobile & Tablet */}\n        <div className=\"lg:hidden space-y-4\">\n          {students.filter(student => student?.id).map((student) => (\n            <Card key={student.id} className=\"p-4\">\n              <div className=\"flex flex-col space-y-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">\n                      {student.user.firstName} {student.user.lastName}\n                    </h3>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                      {student.user.email}\n                    </p>\n                  </div>\n                  <div className=\"flex space-x-1 ml-4\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => router.push(`/admin/students/${student.id}`)}\n                      disabled={loading}\n                    >\n                      <Eye className=\"w-4 h-4\" />\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => router.push(`/admin/students/${student.id}/edit`)}\n                      disabled={loading}\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => handleDelete(student.id, `${student.user.firstName} ${student.user.lastName}`)}\n                      disabled={loading}\n                      className=\"text-red-600 hover:text-red-700\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"font-medium text-gray-700 dark:text-gray-300\">Class:</span>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      {student.currentClass ? `${student.currentClass.name} - ${student.currentSection?.name || 'N/A'}` : 'Not assigned'}\n                    </p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-700 dark:text-gray-300\">Gender:</span>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{getGenderLabel(student.gender)}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-700 dark:text-gray-300\">Phone:</span>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{student.user.phone || '-'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-700 dark:text-gray-300\">DOB:</span>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{formatDate(student.dob)}</p>\n                  </div>\n                </div>\n                \n                <div className=\"pt-2 border-t border-gray-200 dark:border-gray-700\">\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    Admitted: {formatDate(student.createdAt)}\n                  </span>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        {/* Pagination */}\n        {pagination.totalPages > 1 && (\n          <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mt-6\">\n            <div className=\"text-sm text-gray-700 text-center sm:text-left\">\n              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}\n              {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}\n              {pagination.totalCount} students\n            </div>\n            \n            <div className=\"flex justify-center sm:justify-end gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handlePageChange(pagination.page - 1)}\n                disabled={!pagination.hasPrevPage || loading}\n              >\n                <ChevronLeft className=\"w-4 h-4 sm:mr-1\" />\n                <span className=\"hidden sm:inline\">Previous</span>\n              </Button>\n              \n              <div className=\"flex items-center px-3 py-2 text-sm bg-gray-50 rounded-md\">\n                <span className=\"hidden sm:inline\">Page </span>\n                {pagination.page} <span className=\"hidden sm:inline\">of {pagination.totalPages}</span>\n                <span className=\"sm:hidden\">/{pagination.totalPages}</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handlePageChange(pagination.page + 1)}\n                disabled={!pagination.hasNextPage || loading}\n              >\n                <span className=\"hidden sm:inline\">Next</span>\n                <ChevronRight className=\"w-4 h-4 sm:ml-1\" />\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {students.length === 0 && (\n          <div className=\"text-center py-8 text-gray-500\">\n            No students found. {searchTerm || selectedClass || selectedGender ? 'Try adjusting your search criteria.' : 'Add your first student to get started.'}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAqEO,SAAS,aAAa,KAAoD;QAApD,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAqB,GAApD;;IAC3B,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAgB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,0TAAQ,EAAC;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,0TAAQ,EAAC;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,0TAAQ,EAAC;IAErD,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;QACxC,IAAI,eAAe,OAAO,MAAM,CAAC,WAAW;QAC5C,IAAI,gBAAgB,OAAO,MAAM,CAAC,UAAU;QAC5C,OAAO,MAAM,CAAC,QAAQ;QAEtB,OAAO,IAAI,CAAC,AAAC,mBAAoC,OAAlB,OAAO,QAAQ;IAChD;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,OAAO,MAAM,CAAC,UAAU;QACxC,IAAI,eAAe,OAAO,MAAM,CAAC,WAAW;QAC5C,IAAI,gBAAgB,OAAO,MAAM,CAAC,UAAU;QAC5C,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;QAEnC,OAAO,IAAI,CAAC,AAAC,mBAAoC,OAAlB,OAAO,QAAQ;IAChD;IAEA,MAAM,eAAe,OAAO,WAAmB;QAC7C,IAAI,CAAC,QAAQ,AAAC,mCAA8C,OAAZ,aAAY,qCAAmC;YAC7F;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,uBAAgC,OAAV,YAAa;gBAC/D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,wCAAwC;YACxC,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI;QACnB,IAAI,eAAe,OAAO,MAAM,CAAC,WAAW;QAE5C,MAAM,MAAM,AAAC,4BAA6C,OAAlB,OAAO,QAAQ;QACvD,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB;IAC1C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8UAAC,6KAAI;;0BACH,8UAAC,mLAAU;0BACT,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;;8CACC,8UAAC,kLAAS;8CAAC;;;;;;8CACX,8UAAC,wLAAe;8CAAC;;;;;;;;;;;;sCAInB,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,iLAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;;sDAEV,8UAAC,+UAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8UAAC,iLAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,SAAQ;oCACR,UAAU;;sDAEV,8UAAC,yUAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8UAAC,iLAAM;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,UAAU;;sDAEV,8UAAC,mUAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAMzC,8UAAC,oLAAW;;oBACT,uBACC,8UAAC,+KAAK;wBAAC,SAAQ;wBAAc,WAAU;kCACrC,cAAA,8UAAC,0LAAgB;sCAAE;;;;;;;;;;;kCAKvB,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC,+KAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;gDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;;;;;;;0CAK9C,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8UAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAChD,WAAU;;0DAEV,8UAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,QAAQ,MAAM,CAAC,CAAA,MAAO,gBAAA,0BAAA,IAAK,EAAE,EAAE,GAAG,CAAC,CAAC,MACnC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,oBAAA,8BAAA,QAAS,EAAE,EAAE,GAAG,CAAC,CAAC,wBAC/C,8UAAC;wDAAuC,OAAO,AAAC,GAAY,OAAV,IAAI,EAAE,EAAC,KAAc,OAAX,QAAQ,EAAE;;4DACnE,IAAI,IAAI;4DAAC;4DAAI,QAAQ,IAAI;;uDADf,AAAC,GAAY,OAAV,IAAI,EAAE,EAAC,KAAc,OAAX,QAAQ,EAAE;;;;;;;;;;;;;;;;;0CAQ5C,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8UAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8UAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8UAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8UAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8UAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8UAAC,iLAAM;wCAAC,SAAS;wCAAc,WAAU;wCAAS,UAAU;;4CACzD,wBACC,8UAAC,qVAAO;gDAAC,WAAU;;;;;qEAEnB,8UAAC,yUAAM;gDAAC,WAAU;;;;;;4CAClB;;;;;;;;;;;;;;;;;;;kCAOR,8UAAC;wBAAI,WAAU;kCACb,cAAA,8UAAC;4BAAM,WAAU;;8CACf,8UAAC;8CACC,cAAA,8UAAC;wCAAG,WAAU;;0DACZ,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAA+E;;;;;;0DAG7F,8UAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,8UAAC;8CACE,SAAS,MAAM,CAAC,CAAA,UAAW,oBAAA,8BAAA,QAAS,EAAE,EAAE,GAAG,CAAC,CAAC;4CAgBkB;6DAf9D,8UAAC;4CAAoB,WAAU;;8DAC7B,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC;;0EACC,8UAAC;gEAAI,WAAU;;oEACZ,QAAQ,IAAI,CAAC,SAAS;oEAAC;oEAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;0EAEjD,8UAAC;gEAAI,WAAU;0EACZ,WAAW,QAAQ,GAAG;;;;;;;;;;;;;;;;;8DAI7B,8UAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI,CAAC,KAAK;;;;;;8DAErB,8UAAC;oDAAG,WAAU;8DACX,QAAQ,YAAY,GAAG,AAAC,GAAiC,OAA/B,QAAQ,YAAY,CAAC,IAAI,EAAC,OAA2C,OAAtC,EAAA,0BAAA,QAAQ,cAAc,cAAtB,8CAAA,wBAAwB,IAAI,KAAI,SAAU;;;;;;8DAEtG,8UAAC;oDAAG,WAAU;8DACX,eAAe,QAAQ,MAAM;;;;;;8DAEhC,8UAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI,CAAC,KAAK,IAAI;;;;;;8DAEzB,8UAAC;oDAAG,WAAU;8DACX,WAAW,QAAQ,SAAS;;;;;;8DAE/B,8UAAC;oDAAG,WAAU;8DACZ,cAAA,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,iLAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,mBAA6B,OAAX,QAAQ,EAAE;gEACxD,UAAU;0EAEV,cAAA,8UAAC,gUAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8UAAC,iLAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,mBAA6B,OAAX,QAAQ,EAAE,EAAC;gEACzD,UAAU;0EAEV,cAAA,8UAAC,4UAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8UAAC,iLAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,aAAa,QAAQ,EAAE,EAAE,AAAC,GAA4B,OAA1B,QAAQ,IAAI,CAAC,SAAS,EAAC,KAAyB,OAAtB,QAAQ,IAAI,CAAC,QAAQ;gEAC1F,UAAU;gEACV,WAAU;0EAEV,cAAA,8UAAC,6UAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAnDjB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCA8D3B,8UAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,CAAC,CAAA,UAAW,oBAAA,8BAAA,QAAS,EAAE,EAAE,GAAG,CAAC,CAAC;gCA6CwB;iDA5CpE,8UAAC,6KAAI;gCAAkB,WAAU;0CAC/B,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAG,WAAU;;gEACX,QAAQ,IAAI,CAAC,SAAS;gEAAC;gEAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;sEAEjD,8UAAC;4DAAE,WAAU;sEACV,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;8DAGvB,8UAAC;oDAAI,WAAU;;sEACb,8UAAC,iLAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,mBAA6B,OAAX,QAAQ,EAAE;4DACxD,UAAU;sEAEV,cAAA,8UAAC,gUAAG;gEAAC,WAAU;;;;;;;;;;;sEAEjB,8UAAC,iLAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,mBAA6B,OAAX,QAAQ,EAAE,EAAC;4DACzD,UAAU;sEAEV,cAAA,8UAAC,4UAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8UAAC,iLAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,aAAa,QAAQ,EAAE,EAAE,AAAC,GAA4B,OAA1B,QAAQ,IAAI,CAAC,SAAS,EAAC,KAAyB,OAAtB,QAAQ,IAAI,CAAC,QAAQ;4DAC1F,UAAU;4DACV,WAAU;sEAEV,cAAA,8UAAC,6UAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAKxB,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;;sEACC,8UAAC;4DAAK,WAAU;sEAA+C;;;;;;sEAC/D,8UAAC;4DAAE,WAAU;sEACV,QAAQ,YAAY,GAAG,AAAC,GAAiC,OAA/B,QAAQ,YAAY,CAAC,IAAI,EAAC,OAA2C,OAAtC,EAAA,0BAAA,QAAQ,cAAc,cAAtB,8CAAA,wBAAwB,IAAI,KAAI,SAAU;;;;;;;;;;;;8DAGxG,8UAAC;;sEACC,8UAAC;4DAAK,WAAU;sEAA+C;;;;;;sEAC/D,8UAAC;4DAAE,WAAU;sEAAoC,eAAe,QAAQ,MAAM;;;;;;;;;;;;8DAEhF,8UAAC;;sEACC,8UAAC;4DAAK,WAAU;sEAA+C;;;;;;sEAC/D,8UAAC;4DAAE,WAAU;sEAAoC,QAAQ,IAAI,CAAC,KAAK,IAAI;;;;;;;;;;;;8DAEzE,8UAAC;;sEACC,8UAAC;4DAAK,WAAU;sEAA+C;;;;;;sEAC/D,8UAAC;4DAAE,WAAU;sEAAoC,WAAW,QAAQ,GAAG;;;;;;;;;;;;;;;;;;sDAI3E,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC;gDAAK,WAAU;;oDAA2C;oDAC9C,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;+BA/DpC,QAAQ,EAAE;;;;;;;;;;;oBAwExB,WAAW,UAAU,GAAG,mBACvB,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;oCAAiD;oCACpD,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;oCAAE;oCAAI;oCAC3D,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,UAAU;oCAAE;oCAAI;oCACxE,WAAW,UAAU;oCAAC;;;;;;;0CAGzB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;wCAClD,UAAU,CAAC,WAAW,WAAW,IAAI;;0DAErC,8UAAC,4VAAW;gDAAC,WAAU;;;;;;0DACvB,8UAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAGrC,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAK,WAAU;0DAAmB;;;;;;4CAClC,WAAW,IAAI;4CAAC;0DAAC,8UAAC;gDAAK,WAAU;;oDAAmB;oDAAI,WAAW,UAAU;;;;;;;0DAC9E,8UAAC;gDAAK,WAAU;;oDAAY;oDAAE,WAAW,UAAU;;;;;;;;;;;;;kDAGrD,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,iBAAiB,WAAW,IAAI,GAAG;wCAClD,UAAU,CAAC,WAAW,WAAW,IAAI;;0DAErC,8UAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8UAAC,+VAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;oBAM/B,SAAS,MAAM,KAAK,mBACnB,8UAAC;wBAAI,WAAU;;4BAAiC;4BAC1B,cAAc,iBAAiB,iBAAiB,wCAAwC;;;;;;;;;;;;;;;;;;;AAMxH;GAzYgB;;QACC,mSAAS;;;KADV", "debugId": null}}, {"offset": {"line": 1430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,gUAA0B;AAE/C,MAAM,sBAAsB,mUAA6B;AAEzD,MAAM,oBAAoB,iUAA2B;AAErD,MAAM,qBAAqB,kUAA4B;AAEvD,MAAM,kBAAkB,+TAAyB;AAEjD,MAAM,yBAAyB,sUAAgC;AAE/D,MAAM,uCAAyB,4TAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8UAAC,+VAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,4TAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,4TAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,8UAAC,kUAA4B;kBAC3B,cAAA,8UAAC,mUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,8JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,4TAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,gUAA0B;QACzB,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gUAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,4TAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,8UAAC,wUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,sUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,wUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,4TAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,yUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,4TAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,iUAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,iUAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,4TAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,qMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,8UAAC,iMAAY;;0BACX,8UAAC,wMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8UAAC,iLAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;sCAE/D,8UAAC,gUAAG;4BAAC,WAAU;;;;;;sCACf,8UAAC,mUAAI;4BAAC,WAAU;;;;;;sCAChB,8UAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8UAAC,wMAAmB;gBAAC,OAAM;;kCACzB,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,gUAAG;gCAAC,WAAU;;;;;;0CACf,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,4UAAO;gCAAC,WAAU;;;;;;0CACnB,8UAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAxCgB;;QACoC,qMAAQ;;;KAD5C", "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.filter(item => item?.name).map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.filter(item => item?.name).map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,mUAAI;IACJ,QAAA,yUAAM;IACN,UAAA,+UAAQ;IACR,OAAA,sUAAK;IACL,UAAA,mVAAQ;IACR,eAAA,kWAAa;IACb,UAAA,mVAAQ;IACR,WAAA,wVAAS;IACT,UAAA,+UAAQ;IACR,MAAA,oUAAI;IACJ,UAAA,+UAAQ;IACR,MAAA,mUAAI;IACJ,MAAA,mUAAI;IACJ,MAAA,4UAAI;IACJ,eAAA,kWAAa;IACb,OAAA,sUAAK;AACP;AAEe,SAAS,gBAAgB,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB,GAArD;QAuHnB,eAA2B,gBAG3B,oBAAA;;IAzHnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,0SAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,oUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,8UAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,8UAAC,0TAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8UAAC;gCAAI,WAAU;0CACZ,WAAW,MAAM,CAAC,CAAA,OAAQ,iBAAA,2BAAA,KAAM,IAAI,EAAE,GAAG,CAAC,CAAC;oCAC1C,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,8UAAC,iLAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,8UAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,AAAC,UAAmB,OAAV,KAAK,IAAI;;;;;gCAY9B;;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;0BACb,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,yUAAM;oCAAC,WAAU;;;;;;8CAClB,8UAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,8UAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,8UAAC;4BAAI,WAAU;sCACZ,WAAW,MAAM,CAAC,CAAA,OAAQ,iBAAA,2BAAA,KAAM,IAAI,EAAE,GAAG,CAAC,CAAC;gCAC1C,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,8UAAC,iLAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,8UAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,AAAC,WAAoB,OAAV,KAAK,IAAI;;;;;4BAS/B;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8UAAC,mUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC,yUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8UAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,+LAAW;;;;;kDAEZ,8UAAC,iLAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAE,WAAU;;gEACV,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS;gEAAC;gEAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;;;;;;;sEAErD,8UAAC;4DAAE,WAAU;sEACV,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,WAAW;;;;;;;;;;;;8DAGrC,8UAAC;oDAAI,WAAU;8DACb,cAAA,8UAAC,iLAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,8UAAC,6UAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8UAAC;wBAAK,WAAU;kCACd,cAAA,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzJwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}]}