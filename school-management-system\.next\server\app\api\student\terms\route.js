var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/student/terms/route.js")
R.c("server/chunks/6c5cd_next_7ab2fdb3._.js")
R.c("server/chunks/262d9_next-auth_cd52c27f._.js")
R.c("server/chunks/5cc23_openid-client_507cb7ad._.js")
R.c("server/chunks/fbce7_jose_dist_node_cjs_188a244e._.js")
R.c("server/chunks/e1f7c__pnpm_478f83f2._.js")
R.c("server/chunks/[root-of-the-server]__38d9d5e9._.js")
R.m("[project]/school-management-system/.next-internal/server/app/api/student/terms/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/school-management-system/src/app/api/student/terms/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/school-management-system/src/app/api/student/terms/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
